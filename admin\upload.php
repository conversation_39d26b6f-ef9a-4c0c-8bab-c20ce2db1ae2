<?php
ob_start();
include('../webset.php'); 
include('../session.php');

 if ( is_dir ("../uploads/") === false ){
 mkdir("../uploads/");
}

  $path = '../uploads/' ;


function getExtension($str) 
{

         $i = strrpos($str,".");
         if (!$i) { return ""; } 

         $l = strlen($str) - $i;
         $ext = substr($str,$i+1,$l);
         return $ext;
 }

  $valid_formats = array("jpg", "png", "gif", "bmp","jpeg","PNG","JPG","JPEG","GIF","BMP");
  if(isset($_POST) and $_SERVER['REQUEST_METHOD'] == "POST")
    {
      $name = $_FILES['photo']['name'];
      $size = $_FILES['photo']['size'];
      
      
      if(strlen($name))
        {
           $ext = getExtension($name);
          if(in_array($ext,$valid_formats))
          {
          if($size<(1024*1024)) 
            {
              $actual_image_name = str_replace(' ','-',$name);
              $fn = str_replace('.'.$ext , '' , $name );
              $tmp = $_FILES['photo']['tmp_name'];
              if(move_uploaded_file($tmp, $path.$actual_image_name))
                {
                
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'LOGO' ){
                    Update( 'Logo' , 'uploads/'.$actual_image_name );
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'Logo2' ){
                    Update( 'Logo2' , 'uploads/'.$actual_image_name );
                  } 

                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'brands_logo' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE brands SET logo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'brands_cover' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE brands SET cover = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'power_logo' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE power SET logo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'kinds_logo' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE kinds SET logo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'post_photo' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE posts SET photo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'products_photo' && isset($_POST['id']) ){
                    $stmt = $db->prepare("UPDATE products SET photo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'uploads/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }

                  

                }
              else
                echo "Fail upload folder with read access.";
              redirect_home ('back' , 0);
            }
            else
               redirect_home ('back' , 0);     
            }
            else
          redirect_home ('back' , 0);
        }
        
      else
      redirect_home ('back' , 0);
        
      exit;
        

    }


//redirect_home ('back' , 0);

ob_end_flush();
?>
