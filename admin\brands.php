<?php
ob_start();
$Title_page = 'العلامات التجارية';
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php'); 

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('brands' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('brands' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] >= 0){
			UpdateTable('brands' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] > 0){
			UpdateTable('brands' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'brands', 'WHERE id = '.$_GET['id'] );
			header('Location: brands.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
			     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['ar_name'];?> " </b> ؟</h3>
			     <p>برجاء العلم انه سيتم الحذف بشكل نهائي ولا يمكن الرجوع فيه.</p>

			     <center>
			     	<a class="btn btn-danger btn-md" href="brands.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-md" href="brands.php">رجوع</a>
			     </center>
		 
			</div>	
		</div>
	</div>
</div>
<?php
	}else{
		header('Location: brands.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'brands' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel">التعديل على <i class="fa fa-hand-o-left"></i><b> <?php echo $ch[0]['ar_name'].' - '.$ch[0]['en_name'];?></b> </h4>
				</div>
			</div>
		</div>			
	</div>		
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
	                 	$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = $_POST['var3'];  
						$var4  = filter_var($_POST['var4']   , FILTER_SANITIZE_STRING) ;
						$var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;

						if (empty($var1) || empty($var2)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك كتابة الأسم العربى و الإنجليزى');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("UPDATE brands
							    SET ar_name = :var1  ,
									en_name = :var2  ,
							    	fulldescr = :var3,
							    	rotate_deg = :var4 , 
							    	ShowInTop = :var5  
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var3' => $var3 ,
					            'var4' => $var4 ,
					            'var5' => $var5 ,
					            'var0' => $_GET['id']
					          )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

					        redirect_home ('back' , 1); exit();
					    }
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الإسم العربي</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['ar_name'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الإسم الإنجليزى</label>
		                      <input type="text" name="var2" value="<?php echo $ch[0]['en_name'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">وصف شامل للعلامة التجارية . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var3"><?php echo $ch[0]['fulldescr'];?></textarea>
							</div>	
	               	 	</div>	

	               	 	<div class="col-md-6">

							<div class="form-group">
		                      <label class="form-control-label">درجة الدوران</label>
		                      <input type="number" name="var4" value="<?php echo $ch[0]['rotate_deg'];?>" class="form-control ltr">
		                    </div>

		                    <div class="form-group">
								<label class="form-control-label">العرض فى الناف بار</label>
								<select name="var5" class="form-control">
									<option value="1" <?php if($ch[0]['ShowInTop'] == 1){echo 'selected' ;}?>>نعم</option>
									<option value="0" <?php if($ch[0]['ShowInTop'] == 0){echo 'selected' ;}?>>لا</option>
								</select>
		                    </div>
						</div>
						
						
	               	 	
		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                      <a href="brands.php" class="btn btn-lg btn-primary">رجوع</a>
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>


				<?php
					
						echo '<div class="divs"><br><hr>';
						echo '<h4><i class="fa fa-picture-o" aria-hidden="true"></i> صورة العلامة التجارية</h4><p>205*266</p>';
						$ph = 'imgs/Logos/All.png' ;
						if (!empty($ch[0]['logo'])){
							$ph = $ch[0]['logo'];
						}
						?>
						<center>
					    <img style="margin: 20px 0px; width: 205px; max-width: 100%;" src="<?php echo $Site_URL.'/'.$ph ;?>">
					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
					          <input type="file" name="photo" id="photo" required style="display: none;" />
					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
					          <input type="hidden" name="Image_For" value="brands_logo">
					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">
					    </form>
					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>
					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
					  </center>

						<?php
						echo '</div>';

						
					
				?>

			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: brands.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة علامة تجارية جديدة</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
	                 	$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = $_POST['var3'];  
						$var4  = filter_var($_POST['var4']   , FILTER_SANITIZE_STRING) ;
						$var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;

						

						if (empty($var1) || empty($var2)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك كتابة الأسم العربى و الإنجليزى');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO brands ( ar_name , en_name , fulldescr , rotate_deg , ShowInTop  , logo ) 
							 VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4  ,:user_5 ,:user_6 )");  
							$stmt->execute(array(
					          'user_1' => $var1 , 'user_2' =>  $var2  , 'user_3' => $var3 , 'user_4' => $var4  , 'user_5' => $var5 , 'user_6' => 'imgs/Logos/All.png'  )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة علامة تجارية جديدة بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة العلامات التجارية خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=brands.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم العربي</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم الإنجليزى</label>
		                      <input type="text" name="var2" value="" class="form-control ltr">
		                    </div>
	               	 	</div>

						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">وصف شامل للعلامة التجارية . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var3"></textarea>
							</div>	
	               	 	</div>

						<div class="col-md-6">
							<div class="form-group">
		                      <label class="form-control-label">درجة الدوران</label>
		                      <input type="number" name="var4" value="0" class="form-control ltr">
		                    </div>	

							<div class="form-group">
								<label class="form-control-label">العرض فى الناف بار</label>
								<select name="var5" class="form-control">
										<option value="1">نعم</option>
										<option value="0">لا</option>
								</select>
							</div>
	               	 	</div>	

	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="brands.php?do=add_new" class="btn btn-lg btn-success">إضافة علامة تجارية</a>
			</div>
		</div>
	</div>			
</div>


	<?php
	$check = getAllFrom('*' , 'brands' , '', 'ORDER BY orders DESC ,id DESC');
	if(count($check) > 0){
		
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if ($check[$i]['status'] == 0 ){
				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
			}else{
				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
			}
			echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card"><hr>
			<div class="card-body">';
			echo '<div class="divs">';
			echo '<h4 class="cattitlel">'.$check[$i]['orders'].' - '.$check[$i]['ar_name'].' - '.$check[$i]['en_name'].'</h4>';
			echo ' <a href="brands.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary">تعديل</a> ';
			echo ' <a href="brands.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
			echo ' <a href="brands.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '<a href="brands.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
                        	<a href="brands.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>';
			echo '</div>';
			
			
			echo '</div></div></div></div>';
			
		}
	}else{
		echo  '<br>'.Show_Alert('warning' , 'لا يوجد علامات تجارية. ');
	}	

}
?>
<?php
include('footer.php'); 
ob_end_flush();
?>