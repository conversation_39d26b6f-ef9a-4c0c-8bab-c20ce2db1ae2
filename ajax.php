<?php
ob_start();
include('webset.php');
include('session.php');
//-----------------------------------------------------------
if (isset($_POST['type']) && $_POST['type'] == 'products' && isset($_POST['page']) && isset($_POST['PerPage']) ){
	  $page  = filter_var($_POST['page']   , FILTER_SANITIZE_STRING) ;
    $PerPage  = filter_var($_POST['PerPage']   , FILTER_SANITIZE_STRING) ;
    
    $start = $page * $PerPage;	
    if (isset($_POST['productid']) && isset($_POST['powerid']) ){
    $products = getAllFrom('*' , 'products' , 'WHERE status = 1 AND id != "'.$_POST['productid'].'" AND powerid = "'.$_POST['powerid'].'" ' , 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC  LIMIT '.$start.' , '.$PerPage.' ');   
    
    }elseif (isset($_POST['shpbrand']) && isset($_POST['shpower']) ){


      $products = getAllFrom('*' , 'products' , 'WHERE status = 1 '.$_POST['shpbrand'].' '.$_POST['shpower'].' ', 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC LIMIT '.$start.' , '.$PerPage.' ');  

    }else{
    $products = getAllFrom('*' , 'products' , 'WHERE status = 1' , 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC  LIMIT '.$start.' , '.$PerPage.' ');
    }
    for ($i=0; $i <= count($products)-1 ; $i++) { 
		GetProduct($products[$i]['id']);
    }
//-----------------------------------------------------------	
}elseif(isset($_POST['type']) && $_POST['type'] == 'posts' && isset($_POST['page']) && isset($_POST['PerPage'])){ 
  $page  = filter_var($_POST['page']   , FILTER_SANITIZE_STRING) ;
  $PerPage  = filter_var($_POST['PerPage']   , FILTER_SANITIZE_STRING) ;
  
  $start = $page * $PerPage;	
  if (isset($_POST['postid']) && $_POST['postid'] != ""){
    $posts = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND id != "'.$_POST['postid'].'" AND brandid != 10' , 'ORDER BY id DESC LIMIT '.$start.' , '.$PerPage.' ');   
  }else{
  
  $posts = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND brandid != 10 ' , 'ORDER BY id DESC LIMIT '.$start.' , '.$PerPage.' ');
  }
  for ($i=0; $i <= count($posts)-1 ; $i++) { 
    GetPost($posts[$i]['id']);
  }
//-----------------------------------------------------------	
}