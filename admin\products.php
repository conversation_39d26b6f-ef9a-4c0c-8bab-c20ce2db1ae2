<?php
ob_start();
$Title_page = 'المنتجات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'products' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('products' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('products' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'products' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'products', 'WHERE id = '.$_GET['id'] );
			header('Location: products.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'products' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['title'];?> " </b> ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

		     <center>
		     	<a class="btn btn-danger btn-lg" href="products.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-lg" href="products.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
	}else{
		header('Location: products.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة منتج جديد </h4>
				</div>
			</div>
			<br>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = trim(filter_var($_POST['var3']   , FILTER_SANITIZE_STRING)) ;
						$var4  = trim(filter_var($_POST['var4']   , FILTER_SANITIZE_STRING)) ;
						$var5  = $_POST['var5'];  
						$var6  = $_POST['var6'];  
						$var7  = $_POST['var7'];  
						$var8  = $_POST['var8'];  
						$var9  = $_POST['var9'];  
						$var10  = nl2br($_POST['var10']);  
						$var11  = nl2br($_POST['var11']);  
						$var12  = nl2br($_POST['var12']);  
						$var13  = '';

						$b = getAllFrom('*' , 'brands' , 'WHERE id = "'.$var2.'" ', 'ORDER BY id DESC');
						if (count($b) > 0){
							$var13 = trim(str_replace('تكييف' , '' , $b[0]['ar_name'])) . ' - '. trim($b[0]['en_name']);
						}
						
						$stmt = $db->prepare("INSERT INTO products ( title , brandid , powerid , kindid , descr , tags , type , price , code , features , unite1 , unite2 , model , datee , photo) 
							VALUES (:user_1 ,:user_2  ,:user_3  ,:user_4  ,:user_5 ,:user_6 ,:user_7 ,:user_8 ,:user_9 ,:user_10 ,:user_11 ,:user_12 ,:user_13 ,:user_14 ,:user_15 )");  
						$stmt->execute(array(
							'user_1' => $var1  ,
							'user_2' => $var2  ,
							'user_3' => $var3  ,
							'user_4' => $var4  ,
							'user_5' => $var5  ,
							'user_6' => $var6  ,
							'user_7' => $var7  ,
							'user_8' => $var8  ,
							'user_9' => $var9  ,
							'user_10' => $var10  ,
							'user_11' => $var11  ,
							'user_12' => $var12  ,
							'user_13' => $var13  ,
							'user_14' => time()  ,
							'user_15' => 'imgs/Logos/All.png'
							)) ;


						echo '<div class="col-md-12">';
						echo  Show_Alert('success' , 'تم إضافة المنتج بنجاح. ');
						echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة المنتجات خلال 1 ثانيه. ');
						echo '</div>';  		 	
						header("refresh:1;url=products.php");
						exit();
				    	
	                 }
	                 ?>
					<form method="post">
						
                        <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">العنوان</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">العلامة التجارية</label>
		                      <select class="form-control" name="var2">
		                      	<option value="0">إختر البراند</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">'.$cc[$i]['ar_name'].' - '.$cc[$i]['en_name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">قدرة التكييف</label>
		                      <select class="form-control" name="var3">
		                      	<option value="0">إختر القدرة</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'power' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">'.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">نوع التكييف</label>
		                      <select class="form-control" name="var4">
		                      	<option value="0">إختر النوع</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'kinds' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">'.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>

						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">المحتوى . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var5"></textarea>
							</div>	
	               	 	</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">التاجات او الكلمات الدلالية</label>
								<textarea class="form-control to2" name="var6"></textarea>
							</div>	
	               	 	</div>

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">نظام التكييف</label>
		                      <select class="form-control" name="var7">
							    <option value="0">إختر النظام</option>
		                      	<option value="بارد فقط">بارد فقط</option>
		                      	<option value="بارد / ساخن">بارد / ساخن</option>
		                      </select>
		                    </div>
						</div>		 

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">السعر</label>
		                      <input type="number" name="var8" value="0" class="form-control">
		                    </div>
	               	 	</div>  

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">كود الجهاز</label>
		                      <input type="text" name="var9" value="" class="form-control ltr">
		                    </div>
	               	 	</div> 

						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">المميزات - كل ميزة فى سطر</label>
								<textarea class="form-control to2" name="var10"></textarea>
							</div>	
	               	 	</div>		   
						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">الوحدة الداخلية - الأسم=>القيمة</label>
								<textarea class="form-control to2" name="var11"></textarea>
							</div>	
	               	 	</div>		   
						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">الوحدة الخارجية - الأسم=>القيمة</label>
								<textarea class="form-control to2" name="var12"></textarea>
							</div>	
	               	 	</div>	

	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'products' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['title'];?>"</h4>
				</div>
			</div><br>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = trim(filter_var($_POST['var3']   , FILTER_SANITIZE_STRING)) ;
						$var4  = trim(filter_var($_POST['var4']   , FILTER_SANITIZE_STRING)) ;
						$var5  = $_POST['var5'];  
						$var6  = $_POST['var6'];  
						$var7  = $_POST['var7'];  
						$var8  = $_POST['var8'];  
						$var9  = $_POST['var9'];  
						$var10  = nl2br($_POST['var10']);  
						$var11  = nl2br($_POST['var11']);  
						$var12  = nl2br($_POST['var12']);  
						$var13  = '';
						$var14  = $_POST['var14'];  
						$var15  = $_POST['var15'];  

						$b = getAllFrom('*' , 'brands' , 'WHERE id = "'.$var2.'" ', 'ORDER BY id DESC');
						if (count($b) > 0){
							$var13 = trim(str_replace('تكييف' , '' , $b[0]['ar_name'])) . ' - '. trim($b[0]['en_name']);
						}

						$stmt = $db->prepare("UPDATE products
						    SET title = :var1  ,
								brandid = :var2  , 
								powerid = :var3  , 
								kindid = :var4  , 
								descr = :var5  , 
								tags = :var6  , 
								type = :var7  , 
								price = :var8  , 
								code = :var9  , 
								features = :var10  , 
								unite1 = :var11  , 
								unite2 = :var12  , 
								model = :var13  , 
								datee = :var14 , 
								views = :var15  
						    	WHERE  id = :var0 ");  
				           $stmt->execute(array(
                                'var1' => $var1 ,
                                'var2' => $var2 ,
                                'var3' => $var3 ,
                                'var4' => $var4 ,
                                'var5' => $var5 ,
                                'var6' => $var6 ,
                                'var7' => $var7 ,
                                'var8' => $var8 ,
                                'var9' => $var9 ,
                                'var10' => $var10 ,
                                'var11' => $var11 ,
                                'var12' => $var12 ,
                                'var13' => $var13 ,
                                'var14' => strtotime($var14) ,
                                'var15' => $var15 ,
                                'var0' => $_GET['id']
				          )); 	
				        echo '<div class="col-md-12">';
				        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
				        echo '</div>';  		 	

				        redirect_home ('back' , 1); exit();
				    	
	                 }
	                 ?>
					<form method="post">
						
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">العنوان</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['title'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">العلامة التجارية</label>
		                      <select class="form-control" name="var2">
		                      	<option value="0">إختر البراند</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
										if ($ch[0]['brandid']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;} 
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>'.$cc[$i]['ar_name'].' - '.$cc[$i]['en_name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">قدرة التكييف</label>
		                      <select class="form-control" name="var3">
		                      	<option value="0">إختر القدرة</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'power' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
										if ($ch[0]['powerid']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;}   
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>'.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">نوع التكييف</label>
		                      <select class="form-control" name="var4">
		                      	<option value="0">إختر النوع</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'kinds' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
										if ($ch[0]['kindid']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;}
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>'.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>

						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">المحتوى . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var5"><?php echo $ch[0]['descr'];?></textarea>
							</div>	
	               	 	</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">التاجات او الكلمات الدلالية</label>
								<textarea class="form-control to2" name="var6"><?php echo $ch[0]['tags'];?></textarea>
							</div>	
	               	 	</div>

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">نظام التكييف</label>
		                      <select class="form-control" name="var7">
							    <option value="0" <?php if($ch[0]['type'] == '0'){echo 'selected' ;}?>>إختر النظام</option>
		                      	<option value="بارد فقط" <?php if($ch[0]['type'] == 'بارد فقط'){echo 'selected' ;}?>>بارد فقط</option>
		                      	<option value="بارد / ساخن" <?php if($ch[0]['type'] == 'بارد / ساخن'){echo 'selected' ;}?>>بارد / ساخن</option>
		                      </select>
		                    </div>
						</div>		 

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">السعر</label>
		                      <input type="number" name="var8" value="<?php echo $ch[0]['price'];?>" class="form-control">
		                    </div>
	               	 	</div>  

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">كود الجهاز</label>
		                      <input type="text" name="var9" value="<?php echo $ch[0]['code'];?>" class="form-control ltr">
		                    </div>
	               	 	</div> 

						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">المميزات - كل ميزة فى سطر</label>
								<textarea class="form-control to2" name="var10"><?php echo br2nl($ch[0]['features']);?></textarea>
							</div>	
	               	 	</div>		   
						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">الوحدة الداخلية - الأسم=>القيمة</label>
								<textarea class="form-control to2" name="var11"><?php echo br2nl($ch[0]['unite1']);?></textarea>
							</div>	
	               	 	</div>		   
						<div class="col-md-4">
							<div class="form-group">
								<label class="form-control-label">الوحدة الخارجية - الأسم=>القيمة</label>
								<textarea class="form-control to2" name="var12"><?php echo br2nl($ch[0]['unite2']);?></textarea>
							</div>	
	               	 	</div>	

						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">التاريخ</label>
		                      <input type="text" name="var14" value="<?php echo date("Y/m/d H:i" , $ch[0]['datee']);?>" class="form-control">
		                    </div>
	               	 	</div>	

						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">عدد المشاهدات</label>
		                      <input type="number" name="var15" value="<?php echo $ch[0]['views'];?>" class="form-control">
		                    </div>
	               	 	</div>	

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>

				<?php
					
						echo '<div class="divs"><br><hr>';
						echo '<h4><i class="fa fa-picture-o" aria-hidden="true"></i> صورة المنتج</h4><p>500*277</p>';
						$ph = 'imgs/Logos/All.png' ;
						if (!empty($ch[0]['photo'])){
							$ph = $ch[0]['photo'];
						}
						?>
						<center>
					    <img style="margin: 20px 0px; width: 200px; max-width: 100%;" src="<?php echo $Site_URL.'/'.$ph ;?>">
					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
					          <input type="file" name="photo" id="photo" required style="display: none;" />
					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
					          <input type="hidden" name="Image_For" value="products_photo">
					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">
					    </form>
					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>
					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
					  </center>

						<?php
						echo '</div>';
					
				?>
			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: products.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="products.php?do=add_new" class="btn btn-lg btn-success">إضافة منتج جديد </a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all">Product ID</th>
								<th class="all">العنوان</th>
								<th class="all">السعر</th>
                            	<th class="mobile-p desktop">النظام</th>
								<th class="none">العلامة التجارية</th>
                            	<th class="none">القدرة</th>
                            	<th class="none">النوع</th>
                            	<th class="none">الكود</th>
                            	<th class="none">المشاهدات</th>
                            	<th class="none">التاريخ</th>
                            	<th class="mobile-p desktop">الحالة</th>
                            	<th class="mobile-p desktop">الإجراء</th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'products'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [50, 100, 200, 500, 1000, -1],
                  [50, 100, 200, 500, 1000, "كل  المنتجات"]
                ],
                'destroy': true,
                responsive:true,
                "order": [[ 0, "desc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن منتج",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });

	function ChangePrice(productid){
		var price = $('#price_'+productid).val();
		$.post("ajax.php", { action : 'ChangePrice', productid : productid , price : price} ,function(data){ });
	}

</script>