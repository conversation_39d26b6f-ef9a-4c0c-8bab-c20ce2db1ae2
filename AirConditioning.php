<?php
ob_start();
include('webset.php');
include('session.php');
if (isset($_GET['ID']) && isset($_GET['Brand']) && $_GET['ID'] != "" && $_GET['ID'] > 0){
    $id  = filter_var($_GET['ID']   , FILTER_SANITIZE_STRING) ;
    $product = getAllFrom('*' , 'products' , 'WHERE status = 1 AND id = "'.$id.'" ', 'ORDER BY id DESC');
    if (count($product) > 0){
    $stmt = $db->prepare("UPDATE  products SET  views = :edt1  WHERE  id = :edt5 ");     
    $stmt->execute(array('edt1' => $product[0]['views']+1 , 'edt5' => $id  ));

    $Title_page = $product[0]['title'] .' - اسعار وعروض ومواصفات '.$product[0]['title'] ;
    $Page_Description = mb_substr( strip_tags($product[0]['descr']), 0, 300,"utf-8" ).'...';
    $Page_KeyWords = strip_tags($product[0]['tags']);
    $Page_images =  $Site_URL.'/'.$product[0]['photo'];
    }
}
include('header.php'); 
include('navbar.php');
$totalpage =0; 


if (isset($_GET['Brand']) && isset($_GET['Model']) && isset($_GET['ID']) ){
    //echo '<div class="container-fluid">';
    global $Site_URL;	
	$product = getAllFrom('*' , 'products' , 'WHERE id ="'.$_GET['ID'].'" AND status = 1 ', '');
	if (count($product) > 0){
        $brandid = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND id = "'.$product[0]['brandid'].'" ' , '');
        if (count($brandid) > 0){
            $ar_brnad = $brandid[0]['ar_name'];
            $en_brnad = $brandid[0]['en_name'];
            $ph_brand = $brandid[0]['logo'];
            $brandidp = $brandid[0]['id'];
        }else{
            $ar_brnad = 'All';
            $en_brnad = 'All';
            $ph_brand = 'imgs/Logos/All.png';
            $brandidp = 0 ;
        }

        $powerid = getAllFrom('*' , 'power' , 'WHERE status = 1 AND id = "'.$product[0]['powerid'].'" ' , '');
        if (count($powerid) > 0){
            $powern = $powerid[0]['name'];
            $poweri = $powerid[0]['powerint'];
            $powerg = $powerid[0]['logo'];
            $poid   = $powerid[0]['id'];
        }else{
            $powern = 'All';
            $poweri = 'All';
            $powerg = 'imgs/Logos/All.png';
            $poid   = 0 ;
        }

        $kindid = getAllFrom('*' , 'kinds' , 'WHERE status = 1 AND id = "'.$product[0]['kindid'].'" ' , '');
        if (count($kindid) > 0){
            $kindn = $kindid[0]['name'];
            $kinde = $kindid[0]['en'];
            $kindg = $kindid[0]['logo'];
        }else{
            $kindn = 'All';
            $kinde = 'All';
            $kindg = 'imgs/Logos/All.png';
        }

        if ($product[0]['code'] == ""){
            $sucode = $en_brnad;
        }else{
            $sucode = $product[0]['code'];
        }

        $nextyear = date('Y/m/d',strtotime("+1 year"));


        echo '<section itemscope="" itemtype="http://schema.org/Product" class="products-content"><div class="container"><div class="row">';
        
        echo '
        <h2 class="hideclass" style="font-size:26px;line-height: 30px;margin: 0px;margin-bottom: 0px;">'.$product[0]['model'].'<div itemprop="brand" itemtype="http://schema.org/Thing" itemscope="">
                <meta itemprop="name" content="'.trim(str_replace('تكييف' ,'' , $ar_brnad)).'">
            </div>	
        </h2>
        <meta itemprop="sku" content="'.$sucode.'">	
        <meta itemprop="mpn" content="'.$sucode.'">	
        <div class="hideclass" itemprop="aggregateRating"
            itemscope itemtype="http://schema.org/AggregateRating">
            <span itemprop="ratingValue">100</span>
            <span itemprop="bestRating">100</span>
            <span itemprop="ratingCount">'.$product[0]['views'].'</span>
            <meta itemprop="reviewCount" content="'.$product[0]['views'].'">
        </div>
        <span class="hideclass" itemprop="model">'.$en_brnad.'</span>  
        
        <div class="hideclass" itemprop="offers" itemscope itemtype="http://schema.org/Offer">
            <meta itemprop="price" content="'.$product[0]['price'].'">
            <meta itemprop="priceCurrency" content="EGP" />
            <meta itemprop="priceValidUntil" content="'.$nextyear.'" />
            <meta itemprop="url" content="'.$actual_link.'" />
            <link itemprop="availability" href="http://schema.org/InStock" />
        </div>
        <div class="hideclass" itemscope itemtype="http://schema.org/Organization">
        <meta itemprop="name" content="'.$ar_brnad.'">
      </div>
        
      <div class="hideclass" itemprop="review" itemscope itemtype="http://schema.org/Review">
        <span itemprop="name">'.$product[0]['title'].'</span> -
        by <span itemprop="author">'.$Site_Title.'</span>,
        <meta itemprop="datePublished" content="'.date("Y-m-d" , $product[0]['datee'] ).'">
        <div itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
          <meta itemprop="worstRating" content = "1">
          <span itemprop="ratingValue">5</span>
          <span itemprop="bestRating">5</span>
        </div>
        <span itemprop="description">'.mb_substr( strip_tags($product[0]['descr']), 0, 300,"utf-8" ).' ...</span>
      </div>
      <meta itemprop="description" content="'.$product[0]['descr'].'">
        ';

		//-----------------------------
		echo '<div class="col-md-12 pad0">';
		//---
		echo '<h1 itemprop="name">'.$product[0]['title'].'</h1>';
		echo '<div class="col-md-6">';//photo section
        echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
        
		echo '<li><a href="'.$Site_URL.'/'.$product[0]['photo'].'"><img src="'.$Site_URL.'/'.$product[0]['photo'].'" itemprop="image" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';

        echo '</ul></article></div>';
        
        echo '<p class="ctysas" style="border: 0;"><i class="fa fa-money" ></i> السعر :</p><ul class="redftr"><li>';
		echo '<p class="desc2">'.number_format($product[0]['price'] , 2).' جنية</p>';
	    echo '</li></ul>';

	    echo '<p class="ctysas"><i class="fa fa-sort-amount-desc" ></i> مواصفات التكييف :</p><ul class="redftr"><li>';
		echo '<p class="desc">'.nl2br($product[0]['descr']).'</p>';
	    echo '</li></ul>';

	    
        echo'	
            <table class="table-fill">
            <tbody>
                <tr>
                    <th class="text-right" colspan="2">الوحده الداخليه</th>
                </tr>
            </tbody>
        <tbody class="table-hover">
        ';		
        if ($product[0]['unite1'] != ''){
            $featx = preg_replace('/<br\\s*?\/??>/i', '<br>', $product[0]['unite1']);
            $feat = explode('<br>' , $featx  );
            if (count($feat) > 0){
                for ($f=0; $f <= count($feat)-1 ; $f++) { 
                    if ($feat[$f] != ""){
                        try {
                            echo '<tr><td>'.explode('=>' , $feat[$f])[0].'</td><td>'.explode('=>' , $feat[$f])[1].'</td></tr>';
                        } catch (Exception $e) {}
                    }
                }
            }
        }	
        echo '
        </tbody>		
        <tbody>
            <tr>
                <th class="text-right" colspan="2">الوحده الخارجية</th>
            </tr>
        </tbody>
        <tbody class="table-hover">
        ';
        if ($product[0]['unite2'] != ''){
            $featx = preg_replace('/<br\\s*?\/??>/i', '<br>', $product[0]['unite2']);
            $feat = explode('<br>' , $featx  );
            if (count($feat) > 0){
                for ($f=0; $f <= count($feat)-1 ; $f++) { 
                    if ($feat[$f] != ""){
                        try {
                            echo '<tr><td>'.explode('=>' , $feat[$f])[0].'</td><td>'.explode('=>' , $feat[$f])[1].'</td></tr>';
                        } catch (Exception $e) {}
                    }
                }
            }
        }	 
        echo '    
        </tbody>		
        </table>';
        echo '<p class="keywords hideclass">'.$product[0]['tags'].'</p>';
		echo '</div>';
		//---
		echo '<div class="col-md-6">';//desc section
        
		
        
        echo '<p class="ctysas"><i class="fa fa-check-square" ></i> مميزات التكييف :</p><ul class="redftr"><li>';
        if ($product[0]['features'] != ''){
            $featx = preg_replace('/<br\\s*?\/??>/i', '<br>', $product[0]['features']);
            $feat = explode('<br>' , $featx  );
            if (count($feat) > 0){
                for ($f=0; $f <= count($feat)-1 ; $f++) { 
                    if ($feat[$f] != ""){
                        echo '<li class="Features"><i class="fa fa-snowflake-o" aria-hidden="true"></i> '.$feat[$f].' </li><br>';
                    }
                }
                echo '<br>';
            }
        }


	    echo '<p class="ctysas"><i class="fa fa-credit-card" ></i> طريقة الدفع :</p><ul class="redftr"><li>';
		echo '<p class="desc">الدفع عند الإستلام</p>';
	    echo '</li></ul>';


        echo '<button onclick="ShowPhone()" class="callphone btn-block btn-lg" style="border-radius: 0;"> <i class="fa fa-shopping-cart" aria-hidden="true"></i> اضغط هنا للاتصال بالمبيعات</button>';
        
        
        echo '<br><br><center><iframe src="https://www.facebook.com/plugins/like.php?href=https%3A%2F%2Ftakieef.com%2F&width=135&layout=button&action=like&size=large&share=true&height=65&appId=839153756124433" width="135" height="65" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowTransparency="true" allow="encrypted-media"></iframe></center>';

        $post = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND brandid = "'.$brandidp.'" ', 'ORDER BY id DESC LIMIT 1');
            if (count($post) > 0){
                echo '<div class="postinproduct">';
                GetSinglePost($post[0]['id']);
                echo '</div>';
            } 
        
		echo '</div>';
		//---
		
		//---
        echo '</div>';
        

        echo '<div class="col-md-12 pad0">';
        $all = getAllFrom('id' , 'products' , 'WHERE status = 1 AND id != "'.$product[0]['id'].'" AND powerid = "'.$poid.'" ', 'ORDER BY id DESC');
        if (count($all) > 0){
            $totalpage = ceil(count($all) / $ProductsPerPageInProducts);
            echo '<h2 class="text-center mrt50 master-title">تكييفات مماثلة</h2>';
	        echo '<ul id="results"></ul>';
	        echo '<div class="col-md-12 col-xs-12 text-center" id="loadmore"></div>';

        }

        echo '</div>';

        echo'</div></div>';
		echo '</section>';

    }else{
        header('Location: AirConditioning.php');	exit();
    }    
    //echo '</div>';

}else{
    $products = getAllFrom('*' , 'products' , 'WHERE status = 1' , 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC ');
    if (count($products) > 0){
        echo '<div class="container-fluid">';
        echo '<h2 class="text-center mrt50 master-title">كل التكييفات</h2>';
        for ($i=0; $i <= count($products)-1 ; $i++) { 
            GetProduct($products[$i]['id']);
        }
        echo '</div>';
    }else{
        echo '<div class="container-fluid"><div class="col-md-12 col-xs-12">'.Show_Alert('info' , 'لا يوجد منتجات فى الوقت الحالى.').'</div></div>';
    }
    
}
include('footer.php'); 
ob_end_flush();
?>

<script>
var PageNumper = 0; 
var total_pages = parseInt("<?php echo $totalpage ;?>");
$(document).ready(function() {
	GetProductsAjax();
});

function GetProductsAjax(){
	$('#loadmore').html('<i class="fa fa-spinner fa-spin" style="" aria-hidden="true"></i>');

	$.post("ajax.php", {'page':PageNumper , 'type':'products'	,'PerPage':"<?php echo $ProductsPerPageInProducts ; ?>"} , function(data){           
		PageNumper++;
		$('#results').append(data);
		if(PageNumper < total_pages) {
			$('#loadmore').html('<a onclick="GetProductsAjax()" class="master-btn">عرض المزيد</a>');
		}else{
			$('#loadmore').html("");
		}
	});
}
</script>