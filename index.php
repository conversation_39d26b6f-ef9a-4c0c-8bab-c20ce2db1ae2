<?php
if(isset($_GET['route'])){
    header('Location: index.php'); exit();
}
ob_start();
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');
$totalpage = 0;
//------------------------------------------------------
$Icats = getAllFrom('*' , 'brands' , 'WHERE status = 1 ', 'ORDER BY orders DESC, id DESC LIMIT '.GetTableSet ('LimitInIndex'));
if (count($Icats) > 0 ){
	echo '
	
	
	<div class="container">
		<div class="slider-container">
    <img src="img/taqsit.png" class="slider-image">
</div>
</div>
	 
	
	
	
	
	
	<div class="container-fluid">';
	for ($i=0; $i <= count($Icats)-1 ; $i++) { 
	  echo '<div class="col-md-2 col-sm-3 col-xs-6 pad5"> 
			<a class="index-brand" title="'.$Icats[$i]['ar_name'].'" href="'.$Site_URL.'/Search.php?AirConditioningBrand='.$Icats[$i]['ar_name'].'">
				<img alt="'.$Icats[$i]['ar_name'].'" src="'.$Icats[$i]['logo'].'">
				        <p class="namebeandhome">'.$Icats[$i]['ar_name'].'</p>


			</a>
		</div>';
	}
	echo
	 ' </div>';
	
}





$posts = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND brandid != 10 ', 'ORDER BY id DESC LIMIT '.GetTableSet ('LimitInIndexPosts'));
if (count($posts) > 0 ){
	//echo '<div class="container-fluid">';
	echo '<div class="container">';
	echo '<h2 class="text-center mrt50 master-title"><img src="img/offers.png"> عروض وايت اير <img src="img/offers.png"></h2>';
	for ($i=0; $i <= count($posts)-1 ; $i++) { 
			GetPost($posts[$i]['id']);
	  }
	  echo '<div class="col-md-12 col-sm-12 col-xs-12 pad5 text-center"> 	
	  			<h2 class="hideclass">اسعار تكييفات</h2>
				<a href="Posts.php" class="master-btn">شاهد كل العروض</a>	  
			</div>
		';
	echo '</div>';
}


$product = getAllFrom('id' , 'products' , 'WHERE status = 1 ', '');
if (count($product) > 0 ){
	$totalpage = ceil(count($product) / $ProductsPerPage);
	//echo '<div class="container-fluid">';
	echo '<div class="container"><br><br>';
	echo '<h2 class="text-center mrt50 master-title">أفضل المنتجات</h2>';
	echo '<ul id="results"></ul>';
	echo '<div class="col-md-12 col-xs-12 text-center" id="loadmore"></div>';
	echo '</div>';
}





?>





<?php

//------------------------------------------------------
/*

//------------------------------------------------------
if (GetTableSet ('IndexProductType') == 1){
	$category = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = 0 ', 'ORDER BY orders DESC, id DESC');
	if (count($category) > 0 ){
		for ($i=0; $i <= count($category)-1 ; $i++) {
			$sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
			if (count($sub) > 0){
				for ($x=0; $x <= count($sub)-1 ; $x++) { 
					$product = getAllFrom('*' , 'products' , 'WHERE catid = "'.$sub[$x]['id'].'"', 'AND status = 1 ORDER BY id DESC LIMIT '.GetTableSet ('IndexProducts'));
					if (count($product) > 0){
						echo '<section class="aproducts"><div class="container"><div class="row">';
						echo '<h2 class="paid-title"><a href="'.$Site_URL.'/category/'.$sub[$x]['link'].'">'.$sub[$x]['name'].'</a></h2>';
						for ($y=0; $y <= count($product)-1 ; $y++) { 
							GetProduct($product[$y]['id']);
						}
						echo '</div></div></section>';
					}
				}
			}
		}
	}	
}elseif (GetTableSet ('IndexProductType') == 2) {
	$product = getAllFrom('*' , 'products' , '', 'WHERE status = 1 ORDER BY id DESC LIMIT '.GetTableSet ('IndexProducts'));
	if (count($product) > 0){
		echo '<section class="aproducts"><div class="container"><div class="row">';
		echo '<h2 class="paid-title">آخر المنتجات</h2>';
		for ($y=0; $y <= count($product)-1 ; $y++) { 
			GetProduct($product[$y]['id']);
		}
		echo '</div></div></section>';
	}
}
//------------------------------------------------------
*/
include('footer.php'); 
ob_end_flush();
?>
<script>
var PageNumper = 0; 
var total_pages = parseInt("<?php echo $totalpage ;?>");
$(document).ready(function() {
	GetProductsAjax();
});

function GetProductsAjax(){
	$('#loadmore').html('<i class="fa fa-spinner fa-spin" style="" aria-hidden="true"></i>');

	$.post("ajax.php", {'page':PageNumper , 'type':'products'	,'PerPage':"<?php echo $ProductsPerPage ; ?>"} , function(data){           
		PageNumper++;
		$('#results').append(data);
		if(PageNumper < total_pages) {
			$('#loadmore').html('<a onclick="GetProductsAjax()" class="master-btn">عرض المزيد</a>');
		}else{
			$('#loadmore').html("");
		}
	});
}
</script>