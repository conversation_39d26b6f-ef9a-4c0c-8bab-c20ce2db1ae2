<?php
ob_start();
include('../webset.php');
include('../session.php');
//-----------------------------------------------------------
if (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'tags' ){
	$sub = getAllFrom('*' , 'tags' , '', 'ORDER BY id DESC');
	if (count($sub) > 0){
		for ($i=0; $i <= count($sub)-1 ; $i++) { 

			if ($sub[$i]['status'] == 1 ){
				$titx = 'معروض' ; $clsx = 'btn btn-sm btn-success smb' ;
			}else{
				$titx = 'مخفى' ; $clsx = 'btn btn-sm btn-warning smb' ;	
			}
			echo '<tr>
					<td class="padding30">'.$sub[$i]['name'].'</td>
					<td class="padding30">'.$sub[$i]['count'].'</td>
					<td class="padding30"><a href="tags.php?do=show_hide&id='.$sub[$i]['id'].'" class="'.$clsx.'">'.$titx.'</a></td>
					<td class="padding30">
					<a href="tags.php?do=edit&id='.$sub[$i]['id'].'" class="btn btn-sm btn-info smb">تعديل</a> <a href="tags.php?do=del&id='.$sub[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';
		}
	}
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'posts' ){
	$sub = getAllFrom('*' , 'posts' , '', 'ORDER BY id DESC');
	if (count($sub) > 0){
		for ($i=0; $i <= count($sub)-1 ; $i++) { 

			if ($sub[$i]['status'] == 1 ){
				$titx = 'معروض' ; $clsx = 'btn btn-sm btn-success smb' ;
			}else{
				$titx = 'مخفى' ; $clsx = 'btn btn-sm btn-warning smb' ;	
			}
			$b = getAllFrom('*' , 'brands' , 'WHERE id = "'.$sub[$i]['brandid'].'" ', 'ORDER BY id DESC');
			if (count($b) > 0){
				$brand = $b[0]['ar_name'] . ' - '. $b[0]['en_name'];
			}else{
				$brand = 'غير معروف';
			}
			echo '<tr>
					<td class="padding30">'.$sub[$i]['id'].'</td>
					<td class="padding30">'.$sub[$i]['title'].'</td>
					<td class="padding30">'.$brand.'</td>
					<td class="padding30">'.$sub[$i]['views'].' مشاهدة</td>
					<td class="padding30">'.date("Y/m/d H:i" , $sub[$i]['datee']).'</td>
					<td class="padding30"><a href="posts.php?do=show_hide&id='.$sub[$i]['id'].'" class="'.$clsx.'">'.$titx.'</a></td>
					<td class="padding30">
					<a href="posts.php?do=edit&id='.$sub[$i]['id'].'" class="btn btn-sm btn-info smb">تعديل</a> <a href="posts.php?do=del&id='.$sub[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';
		}
	}	
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'products' ){
	$sub = getAllFrom('*' , 'products' , '', 'ORDER BY id DESC');
	if (count($sub) > 0){
		for ($i=0; $i <= count($sub)-1 ; $i++) { 

			if ($sub[$i]['status'] == 1 ){
				$titx = 'معروض' ; $clsx = 'btn btn-sm btn-success smb' ;
			}else{
				$titx = 'مخفى' ; $clsx = 'btn btn-sm btn-warning smb' ;	
			}
			$b = getAllFrom('*' , 'brands' , 'WHERE id = "'.$sub[$i]['brandid'].'" ', 'ORDER BY id DESC');
			if (count($b) > 0){
				$brand = $b[0]['ar_name'] . ' - '. $b[0]['en_name'];
			}else{
				$brand = 'غير معروف';
			}
			$p = getAllFrom('*' , 'power' , 'WHERE id = "'.$sub[$i]['powerid'].'" ', 'ORDER BY id DESC');
			if (count($p) > 0){
				$power = $p[0]['name'] ;
			}else{
				$power = 'غير معروف';
			}
			$k = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$sub[$i]['kindid'].'" ', 'ORDER BY id DESC');
			if (count($k) > 0){
				$kinds = $k[0]['name'] ;
			}else{
				$kinds = 'غير معروف';
			}
			echo '<tr>
					<td class="padding30">'.$sub[$i]['id'].'</td>
					<td class="padding30">'.$sub[$i]['title'].'</td>
					<td class="padding30"><input id="price_'.$sub[$i]['id'].'" onchange="ChangePrice('.$sub[$i]['id'].');" class="inpprice" type="number" value="'.$sub[$i]['price'].'"></td>
					<td class="padding30">'.$sub[$i]['type'].'</td>
					<td class="padding30">'.$brand.'</td>
					<td class="padding30">'.$power.'</td>
					<td class="padding30">'.$kinds.'</td>
					<td class="padding30">'.$sub[$i]['code'].'</td>
					<td class="padding30">'.$sub[$i]['views'].' مشاهدة</td>
					<td class="padding30">'.date("Y/m/d H:i" , $sub[$i]['datee']).'</td>
					<td class="padding30"><a href="products.php?do=show_hide&id='.$sub[$i]['id'].'" class="'.$clsx.'">'.$titx.'</a></td>
					<td class="padding30">
					<a href="products.php?do=edit&id='.$sub[$i]['id'].'" class="btn btn-sm btn-info smb">تعديل</a> <a href="products.php?do=del&id='.$sub[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';
		}
	}	
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'ChangePrice' ){
	$productid = trim(filter_var($_POST['productid']  , FILTER_SANITIZE_STRING)) ;
	$price = trim(filter_var($_POST['price']  , FILTER_SANITIZE_STRING)) ;
	UpdateTable('products' , 'price' ,$price , 'WHERE id = '.$productid  );
//-----------------------------------------------------------------

//-----------------------------------------------------------------	
}