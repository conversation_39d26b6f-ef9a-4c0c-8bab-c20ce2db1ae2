<?php
ob_start();
include('webset.php');
include('session.php');
$postid = '';
if (isset($_GET['ID']) && $_GET['ID'] != "" && $_GET['ID'] > 0){
    $id  = filter_var($_GET['ID']   , FILTER_SANITIZE_STRING) ;
    $post = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND id = "'.$id.'" ', 'ORDER BY id DESC');
    if (count($post) > 0){
        $stmt = $db->prepare("UPDATE  posts SET  views = :edt1  WHERE  id = :edt5 ");     
        $stmt->execute(array('edt1' => $post[0]['views']+1 , 'edt5' => $id  ));

        $brandidx = $post[0]['brandid'];
        if ($post[0]['productid'] > 0){
            $product = getAllFrom('*' , 'products' , 'WHERE status = 1 AND  id = "'.$post[0]['productid'].'" ', ' LIMIT 1');
        }else{
            $product = getAllFrom('*' , 'products' , 'WHERE status = 1 AND price > 0 AND brandid = "'.$brandidx.'" ', 'ORDER BY price ASC , id DESC LIMIT 1');
            $product = array();
        }
        if (count($product) > 0){
            $Title_page = $post[0]['title'] . ' - ' . $product[0]['title'] ;
        }else{
            $Title_page = $post[0]['title'];
        }
        $Page_Description = mb_substr( strip_tags($post[0]['descr']), 0, 300,"utf-8" ).'...';
        $Page_KeyWords = strip_tags($post[0]['tags']);
        $Page_images =  $Site_URL.'/'.$post[0]['photo'];
    }else{
        $all = getAllFrom('id , photo' , 'posts' , 'WHERE status = 1 AND brandid != 10 ', 'ORDER BY id DESC');
        $Title_page = GetTableSet ('PostsPageTitle');
        $Page_Description = GetTableSet ('PostsPageDesc');
        $Page_KeyWords = GetTableSet ('PostsPageKeyWords');
        if (count($all) > 0){
            $Page_images =  $Site_URL.'/'.$all[0]['photo'];
        }
    }
}else{
    $all = getAllFrom('id , photo' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND brandid != 10', 'ORDER BY id DESC');
    $Title_page = GetTableSet ('PostsPageTitle');
    $Page_Description = GetTableSet ('PostsPageDesc');
    $Page_KeyWords = GetTableSet ('PostsPageKeyWords');
    if (count($all) > 0){
        $Page_images =  $Site_URL.'/'.$all[0]['photo'];
    }
}
include('header.php');
include('navbar.php');


if (isset($_GET['ID']) && $_GET['ID'] != "" && $_GET['ID'] > 0){
    $id  = filter_var($_GET['ID']   , FILTER_SANITIZE_STRING) ;
    $post = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND id = "'.$id.'" ', 'ORDER BY id DESC');
    if (count($post) > 0){
        $postid = $post[0]['id'];
        echo '<div class="container">';
        echo '<div class="col-md-6 col-xs-12">';
            echo '
            <h1 class="PostHeading"><span class="hideclass">اسعار خصومات تكييف</span>'.$post[0]['title'].'</h1>	
            <pre class="PostP">'.br2nl($post[0]['descr']).'</pre>
            <p class="keywords hideclass">'.$post[0]['tags'].'</p>	
            <span class="postspan"><i class="fa fa-car" aria-hidden="true"></i> توصيــــل مجانــاً</span>
            <span class="postspan"><i class="fa fa-eye" aria-hidden="true"></i> مشاهدات : '.$post[0]['views'].'</span>	
            <span class="postspan"> '.Get_Date_String($post[0]['datee']).'</span>
            <div class="ofnew">
                <p><img src="img/Paiement.png"> الدفع عند الإستلام</p>
                <p><img src="img/freeshipping.png"> نقل وتركيب مجاناً</p>
                <p><img src="img/call-post.png"> 0237558148</p>
            </div>
            <a href="Posts.php" class="master-btn">شاهد كل العروض</a>	
            ';
        echo '</div>';

        echo '<div class="col-md-6 col-xs-12 text-center">';
            echo '
                <img src="'.$post[0]['photo'].'" alt="'.$post[0]['title'].'" class="PostImg">
            ';
            if (count($product) > 0){
                echo '<div class="postinproduct text-right">';
                GetSignalProduct($product[0]['id']);
                echo '</div>';
            }
        echo '</div>';

        echo '<div class="col-md-12 col-xs-12">';
        echo '<h2 class="text-center mrt50 master-title">خصومات تكييفات '.date("2020").'</h2>';

        $all = getAllFrom('id , photo' , 'posts' , 'WHERE status = 1 AND id != "'.$postid.'"', 'ORDER BY id DESC');
        if (count($all) > 0){
            $PostPerPageInPosts = GetTableSet ('PostPerPageInPosts0');
            $totalpage = ceil(count($all) / $PostPerPageInPosts);
            echo '<ul id="results"></ul>';
            echo '<div class="col-md-12 col-xs-12 text-center" id="loadmore"></div>';
        }
        echo '</div>';
        echo '</div>';
    }else{
        header('Location: Posts.php'); exit();
    }
}else{
    

    echo '<div class="container">';
    echo '<h2 class="text-center mrt50 master-title">أسعار وخصومات التكييفات '.date("Y").'</h2>';

    $all = getAllFrom('id , photo' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND brandid != 10', 'ORDER BY id DESC');
    if (count($all) > 0){
        $totalpage = ceil(count($all) / $PostPerPageInPosts);
        echo '<ul id="results"></ul>';
	    echo '<div class="col-md-12 col-xs-12 text-center" id="loadmore"></div>';
    }
    echo '</div>';
}
include('footer.php');
?>

<script>
var PageNumper = 0; 
var total_pages = parseInt("<?php echo $totalpage ;?>");
var postid = "<?php echo $postid ;?>";
$(document).ready(function() {
	GetPostsAjax();
});

function GetPostsAjax(){
	$('#loadmore').html('<i class="fa fa-spinner fa-spin" style="" aria-hidden="true"></i>');

	$.post("ajax.php", {'page':PageNumper , 'postid' : postid , 'type':'posts'	,'PerPage':"<?php echo $PostPerPageInPosts ; ?>"} , function(data){           
		PageNumper++;
		$('#results').append(data);
		if(PageNumper < total_pages) {
			$('#loadmore').html('<a onclick="GetPostsAjax()" class="master-btn">عرض المزيد</a>');
		}else{
			$('#loadmore').html("");
		}
	});
}
</script>