<?php

echo '<p class="hideclass">'.GetTableSet ('Master_Tags').'</p>';

$allTags = getAllFrom('*' , 'tags' , 'WHERE status = 1' , 'ORDER BY count DESC ,id ASC');
if (count($allTags) > 0 ){
	//echo '<div class="container-fluid">';
	echo '<div style="width:100%;display: inline-block;"><div class="container-fluid"><br><br>';
	echo '<h2 class="text-center mrt50 master-title">الكلمات الدلالية</h2><div class="keywords">';
	for ($i=0; $i <= count($allTags)-1 ; $i++) { 
		echo '<a href="Search.php?typeahead=تكييف+'.trim(str_replace(' ' , '+' , $allTags[$i]['name'])).'" count="'.trim($allTags[$i]['count']).'" class="SearchKeyword">'.trim($allTags[$i]['name']).'</a>';
	}
	echo '</div></div></div>';
}

?>

</div>
	<footer>
	    <div class="container"> 
	      <div class="row"> 
	        <!-- Contact -->
	        <div class="col-md-3 col-xs-12">
	          <h4><i class="fa fa-globe" aria-hidden="true"></i> عن <?php echo $Site_Name ;?></h4>
	          <p><?php echo GetTableSet ('FooterDescr');?></p>
	        </div>
	        <div class="col-md-3 col-xs-12">
	          <h4><i class="fa fa-list" aria-hidden="true"></i> أقسام الموقع</h4>
	          <ul class="links-footer">
				  <?php
					$cats = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC, id DESC LIMIT 8');
					if (count($cats) > 0 ){
						for ($i=0; $i <= count($cats)-1 ; $i++) {
							echo '<div class="col-xs-6 col-sm-6 col-md-6 pad5"><li><a href="'.$Site_URL.'/Search.php?AirConditioningBrand='.$cats[$i]['ar_name'].'"><i class="fa fa-chevron-left"></i> '.$cats[$i]['ar_name'].'</a></li></div>'; 
						}
					}
                ?>	
	          </ul>
	        </div>
	        <div class="col-md-3 col-xs-12">
	          <h4><i class="fa fa-link" aria-hidden="true"></i> روابط تهمك</h4>
	          <ul class="links-footer">
	            <li><a href="<?php echo $Site_URL;?>/about.php"><i class="fa fa-chevron-left"></i> حول الموقع</a></li>
	            <li><a href="<?php echo $Site_URL;?>/ship.php"><i class="fa fa-chevron-left"></i> الشحن والتوصيل</a></li>
	            <li><a href="<?php echo $Site_URL;?>/privacy.php"><i class="fa fa-chevron-left"></i> سياسة الخصوصية</a></li>
	            <li><a href="<?php echo $Site_URL;?>/contactus.php"><i class="fa fa-chevron-left"></i> إتصل بنا</a></li>
	          </ul>
	        </div>

	        <div class="col-md-3 col-xs-12  text-center">
	          <h4><i class="fa fa-superpowers" aria-hidden="true"></i> كن على تواصل معنا</h4>
	          <div class="links">
	          <?php
	          		if (!empty(GetTableSet ('Phone'))){
	          			echo '<p><i class="fa fa-phone-square"></i> الموبايل :  <a href="tel:'.GetTableSet ('Phone').'">'.GetTableSet ('Phone').'</a></p>';
	          		}
	          		if (!empty(GetTableSet ('Phone'))){
	          			echo '<p><i class="fa fa-whatsapp"></i> واتس آب :  <a href="tel:'.GetTableSet ('Phone').'">'.GetTableSet ('Phone').'</a></p>';
	          		}
	          		//if (!empty(GetTableSet ('Social_telegram'))){
	          		//	echo '<p><i class="fa fa-telegram"></i> تليجرام :  <a href="'.GetTableSet ('Social_telegram').'">'.GetTableSet ('Social_telegram').'</a></p>';
	          		//}
	          ?>
			  </div>
	          <div class="social-links text-center">
	          	<?php
	          		if (!empty(GetTableSet ('Social_Face'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_Face').'"><i class="fa fa-facebook"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_twitter'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_twitter').'"><i class="fa fa-twitter"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_insta'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_insta').'"><i class="fa fa-instagram"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_google'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_google').'"><i class="fa fa-google"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_youtube'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_youtube').'"><i class="fa fa-youtube"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_telegram'))){
	          			echo '<a target="_blank" href="'.GetTableSet ('Social_telegram').'"><i class="fa fa-paper-plane-o"></i></a>';
	          		}
	          	?>
	          </div>
	        </div>


	      </div>
	    </div>
	</footer>
	<div class="rights">
	    <div class="container">
	      <div class="row">
	        <div class="col-sm-12 text-center">
	          <p>جميع الحقوق محفوظة © <a href="<?php echo $Site_URL ;?>" class="ri-li"> <?php echo $Site_Name;?> </a></p>
	        </div>
	        

	      </div>
	    </div>
	</div>
	
	<a href="#" onclick="ShowPhone()" class="cd-top"><img src="img/callnow.gif" alt="callus"></a>
	<script>
		var site_url = "<?php echo $Site_URL;?>";
		var SliderMoving = "<?php echo GetTableSet ('SliderMoving');?>";
		var CountSearchData = "<?php echo $CountSearchData;?>";
		var Url = "<?php echo $actual_link;?>";
	</script>
	<script src="<?php echo $Site_URL;?>/layout/js/jquery-1.12.4.js"></script>
	<script src="<?php echo $Site_URL;?>/layout/js/typeahead.min.js"></script>
	<!-- Owl Carousel JS -->
	<!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script> -->
	<script src="<?php echo $Site_URL;?>/layout/js/bootstrap.min.js"></script>
	<script src="<?php echo $Site_URL;?>/layout/js/main.js"></script>
	<script src="https://kenwheeler.github.io/slick/slick/slick.js"></script>
	<script src="<?php echo $Site_URL;?>/layout/js/jquery.desoslide.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.0/wow.js"></script>
	<script src="<?php echo $Site_URL;?>/layout/js/owl.carousel.min.js"></script>
	<script>
		$(".clsfs").click(function(){
			$(".clsfs").removeClass('active');
			$(this).addClass('active');
		});
		$(".clsfc").click(function(){
			$(".clsfc").removeClass('active');
			$(this).addClass('active');
		});
		$(document).ready(function(){
			$('#search-title').typeahead({
				name: 'typeahead',
				remote:'Search.php?key=%QUERY',
				limit : 10
			});
			$( document ).delegate( ".tt-suggestion", "click",function(){	
				var Search = $(this).children("p").html() ;
				window.location.href = "Search.php?typeahead="+Search;	
			});
		});
		function ShowPhone(){
			$("#PhoneData").modal("show");
		}
		function ShowMoreInDescr(){
		    $('#btnshowmoreindescr').hide();
		    $('#showmoreindescr').css('height' , 'auto');
		}

		$('.owl-carousel').owlCarousel({
			rtl: true, // تشغيل من اليمين لليسار
            loop: true, // استمرار التكرار بدون توقف
            margin: 3, // تقليل المسافات بين الأيقونات
            nav: true, // إظهار أزرار التنقل
            dots: false, // إخفاء النقاط السفلية
            autoplay: true, // التشغيل التلقائي
            autoplayTimeout: 2000, // مدة عرض كل عنصر
            autoplayHoverPause: true, // إيقاف الحركة عند تمرير الماوس
            smartSpeed: 800, // تحسين سلاسة الحركة
            autoplaySpeed: 1000, // سرعة الانتقال
            responsive: {
                0: { items: 4 },  // **عرض 2 أيقونة على الموبايل بدل واحدة**
                480: { items: 4 }, // عرض 3 أيقونات على الموبايل المتوسط
                768: { items: 9 }, // عرض 4 أيقونات على التابلت
                1024: { items: 9 } // عرض 5 أيقونات على الشاشات الكبيرة
            }
		})
	</script>
	<?php 
	if (GetTableSet ('MesangerCode') != "" ){
		echo GetTableSet ('MesangerCode'); 
	}  
	?>
   </body>
</html>



