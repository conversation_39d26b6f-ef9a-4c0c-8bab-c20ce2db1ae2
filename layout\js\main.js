$(document).ready(function() { 
	var width =  $(window).width() ;
 	//open dropdown on hover
 	$(".dropdown-hover").hover(function(){
	    $(this).addClass("open");
	    $(this).addClass("active");
	    }, function(){
	    $(this).removeClass("open");
	    $(this).removeClass("active");
	});
 	
	//rate
	var dothis = 0;
	if (dothis == 1){
	$(".cr").hover(function(){
	    $(this).removeClass("fa-star-o");
	    $(this).addClass("fa-star");
	    $(this).addClass("arate");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star-o");
	    pr.addClass("fa-star");
	    pr.addClass("arate");
	    }, function(){
	    $(this).removeClass("fa-star");
	    $(this).removeClass("arate");
	    $(this).addClass("fa-star-o");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star");
	    pr.removeClass("arate");
	    pr.addClass("fa-star-o");
	});
	}
	$(".cr").on("click", function(){
		dothis = 0 ;
	    $(this).removeClass("fa-star-o");
	    $(this).addClass("fa-star");
	    $(this).addClass("arate");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star-o");
	    pr.addClass("fa-star");
	    pr.addClass("arate");
	});
	//open submenu on hover
 	$(".dropdown-submenu").hover(function(){
	    $(this).addClass("open");
	    $(this).addClass("active");
	    }, function(){
	    $(this).removeClass("open");
	    $(this).removeClass("active");
	});
 	//submenu
 	$('.dropdown-submenu a.test').on("click", function(e){
		 $(this).next('ul').toggle();
		 e.stopPropagation();
		 e.preventDefault();
	});
 	//scroll to Top
	var offset = 300,
	offset_opacity = 1200,
	scroll_top_duration = 700,
	$back_to_top = $('.cd-top');

	$(window).scroll(function(){
		( $(this).scrollTop() > offset ) ? $back_to_top.addClass('cd-is-visible') : $back_to_top.removeClass('cd-is-visible cd-fade-out');
		if( $(this).scrollTop() > offset_opacity ) { 
			$back_to_top.addClass('cd-fade-out');
		}
	});
	$back_to_top.on('click', function(event){
		event.preventDefault();
		$('body,html').animate({
			scrollTop: 0 ,
		 	}, scroll_top_duration
	);
	});


	$('#myCarousel').carousel({
        interval: SliderMoving*1000, //changes the speed
        rtl: true,
        autoPlay: true,
        responsive: true,
        merge:true,
		stopOnHover: false,
		pagination: true,
		singleItem: true,
		mouseDrag :true,
    });

	$('#slideshow').desoSlide({
	    thumbs: $('#slideshow_thumbs li > a'),
	    overlay: 'none',
	    controls: {
	        show: false,
	        keys: true
	    }
	});

	$(function () {
	  $('[data-toggle="tooltip"]').tooltip()
	})
});

//--------------------------------------------
function ShowMobileNav(){
	$('.mobile-nav').toggle(500);
}
//--------------------------------------------
function ResetPhoneData(){
	CountSearchData =0;
}

function GetPhoneDataFromProduct(phone){
	CountSearchData = 0;
	GetPhoneData(phone);
}
function GetPhoneData(phone){
		$("#PhoneData").modal("show");
		$('#PhoneDataRes').html('<center><div class="order"><a href="tel:'+phone+'" class="show-phone" id="phone-btn" ><i class="fa fa-phone"></i> '+phone+' <b id="show-phone-res">إضغط هنا للإتصال</b></a></div></center>');
		$.post(site_url+"/ajax.php", { action : 'ShowPhone' , url:Url  } , function(data){});
}
//--------------------------------------------
function ConfirmBuy(productid){
	$("#PhoneData").modal("show");
	$('#PhoneDataRes').html('<center><i class="fa fa-spinner fa-spin fa-font"></i></center>');
	$.post(site_url+"/ajax.php", { action : 'ConfirmBuy' , productid:productid  } , function(data){
		$('#PhoneDataRes').html(data);	
		if (localStorage.getItem('fullname') != null) {
            $('#cv1').val(localStorage.getItem('fullname'));
        }
        if (localStorage.getItem('address') != null) {
            $('#cv2').val(localStorage.getItem('address'));
        }
        if (localStorage.getItem('phone') != null) {
            $('#cv3').val(localStorage.getItem('phone'));
        }
        // if (localStorage.getItem('count') != null) {
        //     $('#cv4').val(localStorage.getItem('count'));
        // }
        // if (localStorage.getItem('size') != null) {
        //     $('#cv5').val(localStorage.getItem('size'));
        // }
        // if (localStorage.getItem('color') != null) {
        //     $('#cv6').val(localStorage.getItem('color'));
        // }


	});
}

function CountChange(num){
	var xn = parseInt($('#cv4').val());
	if (num == 1){
		if (xn < 100){
			$('#cv4').val(xn + 1);
		}else{
			$('#cv4').val(1);
		}
	}else{
		if (xn > 1){
			$('#cv4').val(xn - 1);
		}else{
			$('#cv4').val(1);
		}
	}
	var xn = parseInt($('#cv4').val());
	var p = parseInt($('#cv9').val());
	$('#newprice').html(p*xn);
}

function SaveInputs(){
	var cv1 = $('#cv1').val();
	var cv2 = $('#cv2').val();
	var cv3 = $('#cv3').val();
	// var cv4 = $('#cv4').val();
	// var cv5 = $('#cv5').val();
	// var cv6 = $('#cv6').val();
	localStorage.setItem('fullname', cv1);
	localStorage.setItem('address', cv2);
	localStorage.setItem('phone', cv3);
	// localStorage.setItem('count', cv4);
	// localStorage.setItem('size', cv5);
	// localStorage.setItem('color', cv6);
}

function addOrder(){
	$('#orderres').html('<center><i class="fa fa-spinner fa-spin fa-font"></i></center>');
	var cv0 = $('#cv0').val();
	var cv1 = $('#cv1').val();
	var cv2 = $('#cv2').val();
	var cv3 = $('#cv3').val();
	var cv4 = $('#cv4').val();
	var cv5 = $('#cv5').val();
	var cv6 = $('#cv6').val();
	$.post(site_url+"/ajax.php", { action : 'addOrder' , cv0: cv0 , cv1: cv1 , cv2: cv2 , cv3: cv3 , cv4: cv4 , cv5: cv5 , cv6: cv6   } , function(data){ 
		if (data == 'ok'){
			$('#btnxa').hide(10);
			$('#orderres').html('<div class="index_main"><div class="alert alert-success alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>تم تأكيد الشراء بنجاح، سيتم التواصل معك فى اقرب وقت.</div></div><br><button data-dismiss="modal" class="btn btn-block btn-danger btn-md" style="border-radius: 0;"> إغلاق</button>');
		}else{
			$('#orderres').html(data);
		}
    });

}
//GetPhoneData();
//--------------------------------------------
function SelectReg(){
	$('#var2').html('<option>إختر المنطقة</option>');
	var var1 = $('#var1').val();
	$('#res1').html('');
	if (var1 == '0'){
		$('#var2').addClass('dis');
	}else{
		$.post(site_url+"/ajax.php", { action : 'SelectReg' , id: var1 } , function(data){ 
			if (data != ""){
				$('#var2').html('');
				$('#var2').append(data);
				$('#var2').removeClass('dis');
			}   
    	});
	}
}
//--------------------------------------------
function GetCats(){
	var var1 = $('#var1').val();
	var var2 = $('#var2').val();
	$('#res1').html('');
	if (var1 == '' || var1 == '0' || var2 == '' || var2 == '0'){
		$('#res1').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك إختيار المحافظه و المنطقة.</div></div>');
	}else{
		$('#PhoneDataRes').html('<center><i class="fa fa-spinner fa-spin fa-font"></i></center>');
		$.post(site_url+"/ajax.php", { action : 'GetCats' , var1 : var1 , var2 : var2} , function(data){ 
			$('#PhoneDataRes').html(data);	
		});	
	}
}

//--------------------------------------------
function SelectCategory(){
	var var3 = $('#var3').val();
	$('#res1').html('');
	$('#var4').html('<option>إختر القسم الفرعي</option>');
	if (var3 == '0'){
		$('#var3').addClass('dis');
	}else{
		$.post(site_url+"/ajax.php", { action : 'SelectCategory' , id: var3 } , function(data){ 
			if (data != ""){
				$('#var4').html('');
				$('#var4').append(data);
				$('#var4').removeClass('dis');
			}   
    	});
	}
}
//--------------------------------------------
function ShowResult(){
	var var1 = $('#var1').val();
	var var2 = $('#var2').val();
	var var3 = $('#var3').val();
	var var4 = $('#var4').val();
	if (var3 == '' || var3 == '0' || var4 == '' || var4 == '0'){
		$('#res1').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك إختيار القسم والقسم الفرعي.</div></div>');
	}else{
		CountSearchData = 3;
		$('#PhoneDataRes').html('<center><i class="fa fa-spinner fa-spin fa-font"></i></center>');
		$.post(site_url+"/ajax.php", { action : 'ShowResult' , var1 : var1 , var2 : var2 , var3 : var3, var4 : var4 } , function(data){ 
			$('#PhoneDataRes').html(data);	
		});	
	}
}
//--------------------------------------------
function SelectCity(){
	var var3 = $('#var3').val();
	$('#res').html('');
	$('#var4').html('<option>إختر المنطقة</option>');
	if (var3 == '0'){
		$('#var4').addClass('dis');
	}else{
		$.post(site_url+"/ajax.php", { action : 'SelectCity' , id: var3 } , function(data){ 
			if (data != ""){
				$('#var4').html('');
				$('#var4').append(data);
				$('#var4').removeClass('dis');
				CheckIfReady();
			}   
    	});
	}
	CheckIfReady();
}
//--------------------------------------------
function CheckIfReady(){
	var var1 = $('#var1').val();
	var var2 = $('#var2').val();
	//var var3 = $('#var3').val();
	//var var4 = $('#var4').val();
	if (var1 != "0"){
		$('#res').html('<a class="search-btn" href="'+site_url+'/search.php?category='+var1+'&sub-category='+var2+'">نتيجة البحث</a>');
	}	
}
//--------------------------------------------
function Rate(rate,productid){
	if (User_ID == '0'){
		LoginForm();
	}else{
		dothis = 0;
		for (var i = rate ; i >= 1; i--) {
			$('#s'+i).removeClass("fa-star-o");
	    	$('#s'+i).addClass("fa-star");
	    	$('#s'+i).addClass("arate");
		}
		$.post(site_url+"/ajax.php", { action : 'Rate' , rate: rate , productid: productid } , function(data){ 
			if (data != ""){
				$('#res_rate').html(data);
				window.setTimeout(function() {$("#res_rate").text('')}, 3000);
			}   
		});
	}
}
//--------------------------------------------

//--------------------------------------------
function LoginForm(){
	$('#login_res').html('');
	$('#register_res').html('');
	$("#Form").modal("show");
}
//--------------------------------------------
function Login(){
	$('#login_res').html('');
	$('#register_res').html('');

	var log_var1 = $('#log_var1').val();
	var log_var2 = $('#log_var2').val();
	if (log_var1 == "" || log_var2 == ""){
		$('#login_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة إسم المستخدم وكلمة المرور.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Login' , log_var1: log_var1 , log_var2: log_var2 } , function(data){ 
		if (data != ""){
			$('#login_res').html(data);
		}   
	});
	}
}
//--------------------------------------------
function nLogin(){
	$('#nlogin_res').html('');

	var log_var1 = $('#nlog_var1').val();
	var log_var2 = $('#nlog_var2').val();
	if (log_var1 == "" || log_var2 == ""){
		$('#nlogin_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة إسم المستخدم وكلمة المرور.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Login' , log_var1: log_var1 , log_var2: log_var2 } , function(data){ 
		if (data != ""){
			$('#nlogin_res').html(data);
		}   
	});
	}
}

//--------------------------------------------
function Register(){
	$('#login_res').html('');
	$('#register_res').html('');

	var reg_var1 = $('#reg_var1').val();
	var reg_var2 = $('#reg_var2').val();
	var reg_var3 = $('#reg_var3').val();
	var reg_var4 = $('#reg_var4').val();
	if (reg_var1 == "" || reg_var2 == "" || reg_var3 == "" || reg_var4 == ""){
		$('#register_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك ملئ جميع الحقول.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Register' , reg_var1: reg_var1 , reg_var2: reg_var2 , reg_var3: reg_var3 , reg_var4: reg_var4} , function(data){ 
		if (data != ""){
			$('#register_res').html(data);
		}   
	});
	}
}
//--------------------------------------------
function nRegister(){
	$('#nregister_res').html('');

	var reg_var1 = $('#nreg_var1').val();
	var reg_var2 = $('#nreg_var2').val();
	var reg_var3 = $('#nreg_var3').val();
	var reg_var4 = $('#nreg_var4').val();
	if (reg_var1 == "" || reg_var2 == "" || reg_var3 == "" || reg_var4 == ""){
		$('#nregister_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك ملئ جميع الحقول.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Register' , reg_var1: reg_var1 , reg_var2: reg_var2 , reg_var3: reg_var3 , reg_var4: reg_var4} , function(data){ 
		if (data != ""){
			$('#nregister_res').html(data);
		}   
	});
	}
}




