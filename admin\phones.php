<?php
ob_start();
$Title_page = 'ارقام الموبايلات';
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php'); 

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('phones' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('phones' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] >= 0){
			UpdateTable('phones' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] > 0){
			UpdateTable('phones' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'phones', 'WHERE id = '.$_GET['id'] );
			
			
			header('Location: phones.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
			     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['phone'];?> " </b> ؟</h3>
			     <p>برجاء العلم انه سيتم الحذف بشكل نهائي.</p>

			     <center>
			     	<a class="btn btn-danger btn-md" href="phones.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-md" href="phones.php">رجوع</a>
			     </center>
		 
			</div>	
		</div>
	</div>
</div>
<?php
	}else{
		header('Location: phones.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'phones' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel">التعديل على <i class="fa fa-hand-o-left"></i><b> <?php echo $ch[0]['phone'];?></b> </h4>
				</div>
			</div>
		</div>			
	</div>		
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
					<?php
	                 if (isset($_POST['edit'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
                        $var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
                        $var3  = trim(filter_var($_POST['var3']   , FILTER_SANITIZE_STRING)) ;
                        
                       if (empty($var1) || empty($var2) || empty($var3) ){
                           echo '<div class="col-md-12">';
                           echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول');
                           echo '</div>'; 
                       }else{
							$stmt = $db->prepare("UPDATE phones
							    SET title = :var1 , phone = :var2 , type = :var3  
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var3' => $var3 ,
					            'var0' => $_GET['id']
					          )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

					        redirect_home ('back' , 1); exit();
					    }
	                 }
	                 ?>
					<form method="post">
						
                        <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">رقم الموبايل المعروض</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['title'];?>" class="form-control">
		                    </div>
	               	 	</div>
                        <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">رقم الموبايل الحقيقي</label>
		                      <input type="text" name="var2" value="<?php echo $ch[0]['phone'];?>" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">النوع</label>
		                      <input type="text" name="var3" value="<?php echo $ch[0]['type'];?>" class="form-control">
		                    </div>
	               	 	</div>

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                      <a href="phones.php" class="btn btn-lg btn-primary">رجوع</a>
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>


			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: phones.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة رقم جديد</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
	                 	$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
	                 	$var3  = trim(filter_var($_POST['var3']   , FILTER_SANITIZE_STRING)) ;
	                 	
						if (empty($var1) || empty($var2) || empty($var3) ){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO phones (title , phone , type ) 
							 VALUES (:user_1 , :user_2 ,:user_3 )");  
							$stmt->execute(array(
					          'user_1' => $var1 , 'user_2' => $var2 , 'user_3' => $var3 )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة رقم جديد بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الارقام خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=phones.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
					    
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">رقم الموبايل المعروض</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>
                        <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">رقم الموبايل الحقيقي</label>
		                      <input type="text" name="var2" value="" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">النوع</label>
		                      <input type="text" name="var3" value="" class="form-control">
		                    </div>
	               	 	</div>

	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="phones.php?do=add_new" class="btn btn-lg btn-success">إضافة رقم جديد</a> 
			</div>
		</div>
	</div>			
</div>


	<?php
	echo '<div class="row">
	<div class="col-md-12 col-sm-12">';
	
    $checkx = getAllFrom('*' , 'phones' , '', 'GROUP BY type');
    if (count($checkx) > 0){
        for ($x=0; $x <= count($checkx)-1 ; $x++) { 
            $check = getAllFrom('*' , 'phones' , 'WHERE type = "'.$checkx[$x]['type'].'" ', 'ORDER BY orders DESC ,id DESC');
            if(count($check) > 0){
                echo '<hr><h4>'.$checkx[$x]['type'].'</h4><br>';
                for ($i=0; $i <= count($check)-1 ; $i++) { 
                    if ($check[$i]['status'] == 0 ){
                        $tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
                    }else{
                        $tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
                    }
                    echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card">
                    <div class="card-body">';
                    echo '<div class="divs">';
                    echo '<h4 class="cattitlel">'.$check[$i]['orders'].' - '.$check[$i]['phone'].'</h4>';
                    echo ' <a href="phones.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary">تعديل</a> ';
                    echo ' <a href="phones.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
                    echo ' <a href="phones.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
                    echo '<a href="phones.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
                                    <a href="phones.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>';
                    echo '</div>';
                    
            
                    echo '</div></div></div></div>';
                    
                }
            }else{
                echo  '<br>'.Show_Alert('warning' , ' لا يوجد أى ارقام. ');
            }
        }
    }
		
	echo '</div>';


	echo '</div>';

}
?>
<?php
include('footer.php'); 
ob_end_flush();
?>