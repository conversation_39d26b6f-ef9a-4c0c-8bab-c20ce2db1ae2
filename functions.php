<?php
ob_start();

function GetRate($productid){
	$rate = 0 ;
	$count = 0 ;
	$p = getAllFrom('*' , 'products' , 'WHERE id = "'.$productid.'" ', 'AND status = 1');
	if (count($p) > 0){
		$r = getAllFrom('count(id),sum(rate)' , 'ratings' , 'WHERE productid = "'.$productid.'" ', '');
		if (count($r) > 0){
			if($r[0]['sum(rate)'] > 0 && $r[0]['count(id)'] > 0){
				$count = $r[0]['count(id)'];
				$rate = $r[0]['sum(rate)']/$r[0]['count(id)'];	
			}
		}
		$rate = ceil($rate);
		echo '<p class="rev">';
		for ($i=1; $i <= 5 ; $i++) { 
			if ($rate >= $i){
				echo '<i class="fa fa-star"></i>';
			}else{
				echo '<i class="fa fa-star-o"></i>';
			}
		}
		if ($count == 0){
			$tit = 'لم يتم تقييمه';
		}elseif ($count == 2){
			$tit = 'تقييمان';
		}elseif ($count > 2){
			$tit = $count.' تقييمات';
		}else{
			$tit = $count.' تقييم';
		}
		echo '<span class="margin-right-10">'.$tit.'</span></p>';

	}
}
//-------------------------------------------
function GetProduct($productid){
	global $Site_URL;
	$products = getAllFrom('*' , 'products' , 'WHERE id = "'.$productid.'" ', 'AND status = 1');
	if (count($products) > 0){
		
		$brandid = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND id = "'.$products[0]['brandid'].'" ' , '');
        if (count($brandid) > 0){
            $ar_brnad = $brandid[0]['ar_name'];
            $en_brnad = $brandid[0]['en_name'];
            $ph_brand = $brandid[0]['crs_logo'];
        }else{
            $ar_brnad = 'All';
            $en_brnad = 'All';
            $ph_brand = 'imgs/Logos/All.png';
        }
        $powerid = getAllFrom('*' , 'power' , 'WHERE status = 1 AND id = "'.$products[0]['powerid'].'" ' , '');
        if (count($powerid) > 0){
            $powern = $powerid[0]['name'];
            $poweri = $powerid[0]['powerint'];
            $powerg = $powerid[0]['crs_logo'];
        }else{
            $powern = 'All';
            $poweri = 'All';
            $powerg = 'imgs/Logos/All.png';
        }

        $kindid = getAllFrom('*' , 'kinds' , 'WHERE status = 1 AND id = "'.$products[0]['kindid'].'" ' , '');
        if (count($kindid) > 0){
            $kindn = $kindid[0]['name'];
            $kindi = $kindid[0]['en'];
            $kindg = $kindid[0]['crs_logo'];
        }else{
            $kindn = 'All';
            $kindi = 'All';
            $kindg = 'imgs/Logos/All.png';
        }

        if ($products[0]['type'] == 'بارد / ساخن'){
            $tyimg = GetTableSet ('Hot_Cold');
        }else{
            $tyimg = GetTableSet ('Cold_Only');
        }

        if ($products[0]['price'] != "" && $products[0]['price'] > 0 ){
            $price = '<span class="ACPrice">'.$products[0]['price'].' <span>EGP</span></span>';
        }else{
            $price = '<span class="ACPrice NoPrice" onclick="OpenCallDiv();return false;">السعر الآن</span>';
        }


        echo '<div class="col-md-4 col-xs-12">
                
                <div class="new_prods">
                
                <a href="AirConditioning.php?Brand='.$en_brnad.'&Model='.$products[0]['title'].'&ID='.$products[0]['id'].'">
                    
                    <div class="col-md-12 col-xs-12 pad0 showonmob">
                        <img class="img-responsive" src="'.$Site_URL.'/'.$products[0]['photo'].'" alt="'.$Site_URL.'/'.$products[0]['title'].'">
                    </div>
                    
                    
                    <div class="col-md-12 col-xs-12 pad0 showinpc">
                        <img class="img-responsive" src="'.$Site_URL.'/'.$products[0]['photo'].'" alt="'.$Site_URL.'/'.$products[0]['title'].'">
                    </div>
                    <div class="info-prods">
                       <div class="didels_protact">
                       <div class="col-md-6 pad5">
                       <div class="icon-didels_protact"><img src="img/brandair-conditioner.png" style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"> '.$en_brnad .'</div>
                       <div class="icon-didels_protact"><img src="img/airhorse.png
" div=""style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"/div> '.$powern.'</div>
                       <div class="icon-didels_protact"><img src="img/Cooling-type.png
" div=""style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"/div> '.$products[0]['type'].'</div>
                        </div>
                        
                        <div class="col-md-6 pad5">
                       <div class="icon-didels_protact"><img src="img/Securityair.png" div=""style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"/div> ضمان '.$products[0]['security'] .' سنوات</div>
                       <div class="icon-didels_protact"><img src="img/Space.png
" div=""style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"/div> يغطي '.$products[0]['space'].' متر</div>
                       <div class="icon-didels_protact-price"><img src="img/price-tag.png
" div=""style="width: 28px;height: 28px;border-radius: 11px;background-color: #ffffff;margin: 1px;padding: 2px;border: 0px solid #FFC107;"/div> '.number_format( $products[0]['price'], 2).' </div>
                        </div>


                       </div>
                       
                     
                        
                    </div>
                    <div class="col-md-12 col-xs-12 pad0">
                    <h2>'.$products[0]['title'].'</h2>
                        <span>مشاهدة عروض التكييف</span>
                                           
                    </div>
                    
               
                </a>
                    
                    <p class="hideclass">'.$products[0]['tags'].'</p>
                </div>
                
            </div>
        ';


		// echo '<div class="col-md-3 col-xs-6 pad5"><div class="owl-item"><div class="product">
		// <a href="AirConditioning.php?Brand='.$en_brnad.'&Model='.$products[0]['title'].'&ID='.$products[0]['id'].'">
		// <article>
        // <img class="img-responsive" src="'.$Site_URL.'/'.$products[0]['photo'].'" alt="'.$Site_URL.'/'.$products[0]['title'].'">';	   
        // //echo '<span class="tag">'.$cname.'</span>';
		// echo '<h3 class="tittle">'.$products[0]['title'].'</h3>';
		// echo '
		// 	<p style=" font-size: 11px; color: #ef3b3b; border: 1px solid #d2d2d2; border-radius: 20px; padding: 4px;"><b><i class="fa fa-diamond" aria-hidden="true"></i>	الماركة  :</b>'.$en_brnad .'</p>
		//         <p style=" color: #ffa500; font-size: 11px; border: 1px solid #c5c5c5; border-radius: 20px;  padding: 4px;"><b><i class="fa fa-bolt" aria-hidden="true"></i>	الموديل :</b> '.$products[0]['type'].'</p>
		        
		        
		// 	<p style=" color: #17ab9d; font-size: 11px; border: 1px solid #c7c7c7; border-radius: 21px; padding: 5px;"><b><i class="fa fa-snowflake-o" aria-hidden="true"></i>	القدرة  :</b> '.$powern.'</p>
		// 	';
		// echo '<h5 class="pricep text-center">'.number_format( $products[0]['price'], 2).' جنية</h5>';

		// echo '<h3 class="hideclass"> مميزات وعيوب '.$products[0]['title'].'</h3>';
		// echo '<h3 class="hideclass"> مواصفات '.$products[0]['title'].'</h3>';
		// echo '<h3 class="hideclass"> سعر '.$products[0]['title'].'</h3>';
		// echo '<p class="hideclass">'.$products[0]['tags'].'</p>';
        // //echo '<a onclick="" class="buynow">شراء الآن</a>';
		// echo '</article></a>
		// </div></div></div>';
	}
}
function GetSignalProduct($productid){
	global $Site_URL;
	$products = getAllFrom('*' , 'products' , 'WHERE id = "'.$productid.'" ', 'AND status = 1');
	if (count($products) > 0){
		
		$brandid = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND id = "'.$products[0]['brandid'].'" ' , '');
        if (count($brandid) > 0){
            $ar_brnad = $brandid[0]['ar_name'];
            $en_brnad = $brandid[0]['en_name'];
            $ph_brand = $brandid[0]['logo'];
        }else{
            $ar_brnad = 'All';
            $en_brnad = 'All';
            $ph_brand = 'imgs/Logos/All.png';
        }
        $powerid = getAllFrom('*' , 'power' , 'WHERE status = 1 AND id = "'.$products[0]['powerid'].'" ' , '');
        if (count($powerid) > 0){
            $powern = $powerid[0]['name'];
            $poweri = $powerid[0]['powerint'];
            $powerg = $powerid[0]['logo'];
        }else{
            $powern = 'All';
            $poweri = 'All';
            $powerg = 'imgs/Logos/All.png';
        }

        if ($products[0]['type'] == 'بارد / ساخن'){
            $tyimg = 'sys/Img/Icons/CoolHot.png';
        }else{
            $tyimg = 'sys/Img/Icons/Cool.png';
        }

        if ($products[0]['price'] != "" && $products[0]['price'] > 0 ){
            $price = '<span class="ACPrice">'.$products[0]['price'].' <span>EGP</span></span>';
        }else{
            $price = '<span class="ACPrice NoPrice" onclick="OpenCallDiv();return false;">السعر الآن</span>';
        }


		echo '<div class="col-md-12 col-xs-12 pad5"><div class="owl-item"><div class="product">
		
		<article>
        <a href="AirConditioning.php?Brand='.$en_brnad.'&Model='.$products[0]['title'].'&ID='.$products[0]['id'].'"><img class="img-responsive" src="'.$Site_URL.'/'.$products[0]['photo'].'" alt="'.$Site_URL.'/'.$products[0]['title'].'">';	   
        //echo '<span class="tag">'.$cname.'</span>';
		echo '<h3 class="tittle">'.$products[0]['title'].'</h3>';
		echo '
			<p><b><i class="fa fa-diamond" aria-hidden="true"></i>	الماركة :</b> '.$en_brnad .'</p>
			<p><b><i class="fa fa-snowflake-o"true"></i>	الموديل :</b> '.$products[0]['type'].'</p>
			<p><b><i class="fa fa-snowflake-o" aria-hidden="true"></i>	القدرة  :</b> '.$powern.'</p>
			';
		echo '<h5 class="pricep text-center">'.number_format( $products[0]['000'], 2).' جنية</h5>';

		echo '<h3 class="hideclass"> مميزات وعيوب '.$products[0]['title'].'</h3>';
		echo '<h3 class="hideclass"> مواصفات '.$products[0]['title'].'</h3>';
		echo '<h3 class="hideclass"> سعر '.$products[0]['title'].'</h3>';
        //echo '<a onclick="" class="buynow">شراء الآن</a>';
		echo '</article></a>
		</div></div></div>';
	}
}
//--------------------------------------------------------
function GetPost($postid){
	global $Site_URL;
	$posts = getAllFrom('*' , 'posts' , 'WHERE id = "'.$postid.'"  ', 'AND status = 1');
	if (count($posts)){
	echo '
	<div class="col-md-4 col-sm-6 col-xs-12 pad5"> 
		<div class="posts text-center">
			<a title="'.$posts[0]['title'].'" href="'.$Site_URL.'/Posts.php?Tobic='.$posts[0]['title'].'&ID='.$posts[0]['id'].'">
				<img alt="'.$posts[0]['title'].'" src="'.$posts[0]['photo'].'">
				<h3>'.$posts[0]['title'].'</h3>
			</a>

			<h3 class="hideclass">'.$posts[0]['title'].' عروض تكييفات</h3>
			<p class="hideclass">'.strip_tags($posts[0]['tags']).'</p> 

		</div>
	</div>
		';
	}
}

function GetSinglePost($postid){
	global $Site_URL;
	$posts = getAllFrom('*' , 'posts' , 'WHERE id = "'.$postid.'" ', 'AND status = 1');
	if (count($posts)){
	echo '
	<div class="col-md-12 col-sm-12 col-xs-12 pad5"> 
		<div class="posts text-center">
			<a title="'.$posts[0]['title'].'" href="'.$Site_URL.'/Posts.php?Tobic='.$posts[0]['title'].'&ID='.$posts[0]['id'].'">
				<img alt="'.$posts[0]['title'].'" src="'.$posts[0]['photo'].'">
				<h3>'.$posts[0]['title'].'</h3>
			</a>

			<h3 class="hideclass">'.$posts[0]['title'].' عروض تكييفات</h3>
			<p class="hideclass">'.strip_tags($posts[0]['tags']).'</p> 

		</div>
	</div>
		';
	}
}
//--------------------------------------------------------
function GetBreadcrumb($parts){
	global $Site_URL;
	echo '<ol class="breadcrumb"><li><a href="'.$Site_URL.'">الرئيسية</a></li>';
	for ($i=0; $i <= count($parts)-1 ; $i++) { 
		$tit = str_replace('-', ' ', urldecode($parts[$i]));
		if ($tit != 'category' && $tit != 'brands'){
			if ($i == count($parts)-1){
				echo '<li class="active">'.$tit.'</li>';
			}else{
				echo '<li>'.$tit.'</li>';
			}
		}
	}
	echo '</ol>';
}
//--------------------------------------------------------
function Get_Category_Page($catid , $parts){
	$cid = getAllFrom('*' , 'category' , 'WHERE id = "'.$catid.'" AND status = 1', '');
	if (count($cid) > 0){
		$cname = $cid[0]['name'];
		$parent = $cid[0]['parent'];
	}else{
		$cname = 'غير معروف';
		$parent = 0;
	}
	echo '<section class="paid-product"><div class=" container"><div class="row"><div class="col-md-12">';
	GetBreadcrumb($parts);
	echo '<h2 class="paid-title">'.$cname.'</h2>';
	if ($parent == 0){
		$q = "  ";
		$sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
		if (count($sub) > 0){
			$q = " OR ";
			for ($i=0; $i <= count($sub)-1 ; $i++) { 
				if ($i == count($sub)-1){
					$q = $q .' catid = '.$sub[$i]['id'];
				}else{
					$q = $q .' catid = '.$sub[$i]['id'] . ' OR';	
				}
			}
		}
		$paid = getAllFrom('*' , 'products' , 'WHERE catid ="'.$catid.'" '.$q.' AND status = 1 ', '  ORDER BY paid DESC , id DESC');
	}else{
		$paid = getAllFrom('*' , 'products' , 'WHERE catid ="'.$catid.'"', 'AND status = 1 ORDER BY paid DESC , id DESC');	
	}
	if (count($paid) > 0){
		for ($i=0; $i <= count($paid)-1 ; $i++) { 
			GetProduct($paid[$i]['id']);
		}
	}else{

		echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
	}
	echo '</div></div></div></section>';
}
//--------------------------------------------------------
function Get_brands_Page($brandsid , $parts){
	$cid = getAllFrom('*' , 'brands' , 'WHERE id = "'.$brandsid.'" AND status = 1', '');
	if (count($cid) > 0){
		$cname = $cid[0]['name'];
		$cn = $cid[0]['name'];
	}else{
		$cname = 'غير معروف';
		$cn = '';
	}
	echo '<section class="paid-product"><div class=" container"><div class="row"><div class="col-md-12">';
	GetBreadcrumb($parts);
	echo '<h2 class="paid-title">'.$cname.'</h2>';

	$q ='';
	$tit = explode(' ', $cn);
	if (count($tit) > 0){
		$q = ' WHERE ( ';
		$limit = 5;
		if ($limit > count($tit)) {$limit = count($tit);}
		for ($i=0; $i <= $limit-1 ; $i++) { 
			if ($i == $limit-1){
				$q = $q .' title LIKE "%'.$tit[$i].'%" )';
			}else{
				$q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
			}
		}
	}	
	
	if ($q != ''){
		$more = getAllFrom('*' , 'products' , $q , 'AND status = 1 ORDER BY paid DESC , id DESC');

		if (count($more) > 0){
			for ($i=0; $i <= count($more)-1 ; $i++) { 
				GetProduct($more[$i]['id']);
			}
		}else{
			echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
		}
	}else{
		echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
	}
	echo '</div></div></div></section>';
}
//--------------------------------------------------------
function Get_Seller_Page($userid , $parts){
	$cid = getAllFrom('*' , 'users' , 'WHERE id = "'.$userid.'" ', '');
	if (count($cid) > 0){
		$cname = $cid[0]['username'];
	}else{
		$cname = 'غير معروف';
	}
	echo '<section class="paid-product"><div class="container"><div class="row"><div class="col-md-12 ">';
	GetBreadcrumb($parts);
	echo '<h2 class="paid-title">جميع منتجات '.$cname.'</h2>';
	
		
	$paid = getAllFrom('*' , 'products' , 'WHERE userid ="'.$userid.'" AND status = 1', '  ORDER BY paid DESC , id DESC');
	
	if (count($paid) > 0){
		for ($i=0; $i <= count($paid)-1 ; $i++) { 
			GetProduct($paid[$i]['id']);
		}
	}else{
		echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
	}
	echo '</div></div></div></section>';
}
//--------------------------------------------------------
function Get_Product_Content($productid){
	global $Site_URL;	
	$product = getAllFrom('*' , 'products' , 'WHERE id ="'.$productid.'" AND status = 1 ', '');
	if (count($product) > 0){
		$photos = array();
		$def = GetTableSet ('DefaultImage');
		if (empty($product[0]['photo'])){
			$photos = array($def);
		}else{
			$p = explode('&&&', $product[0]['photo']);
			if(count($p) >0){
				for ($i=0; $i <= count($p)-1 ; $i++) { 
					if ($p[$i] != ""){
						$l = getAllFrom('*' , 'photos' , 'WHERE id ="'.$p[$i].'" ', '');
						if (count($l) >0){
							array_push($photos, $l[0]['link']);
						}
					}
				}
			}
		}

		$catname = '';
		$canm = getAllFrom('*' , 'category' , 'WHERE id ="'.$product[0]['catid'].'" ', '');
		if (count($canm) > 0){
			$catname = '<a href="'.$Site_URL.'/category/'.$canm[0]['link'].'">'.$canm[0]['name'].'</a>';
		}else{
			$catname = 'غير معروف';
		}

		
		echo '<section class="products-content"><div class="container"><div class="row">';
		echo '<div class="col-md-12">';
		GetBreadcrumb(array($product[0]['title']));
		echo '</div>';
		//-----------------------------
		echo '<div class="col-md-12">';
		//---
		echo '<h1>'.$product[0]['title'].'</h1>';
		echo '<h2 class="catg">القسم : '.$catname.'</h2><br>';
		echo '<div class="col-md-6">';//photo section
		echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
			if (count($photos) > 0){
				for ($i=0; $i <=count($photos)-1 ; $i++) { 
					echo '<li><a href="'.$Site_URL.'/'.$photos[$i].'"><img src="'.$Site_URL.'/'.$photos[$i].'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
				}
			}else{
				echo '<li><a href="'.$Site_URL.'/'.$def.'"><img src="'.$Site_URL.'/'.$def.'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
				
			}
		echo '</ul></article></div>';
		echo '</div>';
		//---
		echo '<div class="col-md-6">';//desc section

		echo '<p class="ctysas" style="border: 0;"><i class="fa fa-money" ></i> السعر :</p><ul class="redftr"><li>';
		echo '<p class="desc">'.number_format($product[0]['price'] , 2).' جنية</p>';
	    echo '</li></ul>';

	    echo '<p class="ctysas"><i class="fa fa-sort-amount-desc" ></i> مواصفات المنتج :</p><ul class="redftr"><li>';
		echo '<p class="desc">'.nl2br($product[0]['descr']).'</p>';
	    echo '</li></ul>';

	    if (!empty($product[0]['size'])){
	    	$ps = explode('&&', $product[0]['size']);
	    	if (count($ps) > 0){
	    	echo '<p class="ctysas"><i class="fa fa-shopping-bag" ></i> المقاسات المتاحة :</p><ul class="redftr">';	
	    		for ($i=0; $i <= count($ps)-1 ; $i++) { 
	    			if ($ps[$i] != ''){
	    				echo '<li class="dinb clsfs"><p class="desc">'.$ps[$i].'</p></li>';
	    			}
	    		}
	    	echo '</ul>';	
	    	}
	    }

	    if (!empty($product[0]['color'])){
	    	$ps = explode('&&', $product[0]['color']);
	    	if (count($ps) > 0){
	    	echo '<p class="ctysas"><i class="fa fa-smile-o" ></i> الألوان المتاحة :</p><ul class="redftr">';	
	    		for ($i=0; $i <= count($ps)-1 ; $i++) { 
	    			if ($ps[$i] != ''){
	    				echo '<li class="dinb clsfc"><p class="desc">'.$ps[$i].'</p></li>';
	    			}
	    		}
	    	echo '</ul>';	
	    	}
	    }

	    echo '<p class="ctysas"><i class="fa fa-credit-card" ></i> طريقة الدفع :</p><ul class="redftr"><li>';
		echo '<p class="desc">الدفع عند الإستلام</p>';
	    echo '</li></ul>';


		echo '<button onclick="ConfirmBuy(`'.$product[0]['id'].'`)" class="callphone btn-block btn-lg" style="border-radius: 0;"> <i class="fa fa-shopping-cart" aria-hidden="true"></i> شراء الآن</button>';
		echo '</div>';
		//---
		
		//---
		echo '</div></div></div>';
		//-----------------------------
		$tit = explode(' ', $product[0]['title']);
		if (count($tit) > 0){
			$q = ' WHERE ';
			$limit = 5;
			if ($limit > count($tit)) {$limit = count($tit);}
			for ($i=0; $i <= $limit-1 ; $i++) { 
				if ($i == $limit-1){
					$q = $q .' title LIKE "%'.$tit[$i].'%" ';
				}else{
					$q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
				}
			}
			
			$more = getAllFrom('*' , 'products' , /*$q*/ 'WHERE catid = "'.$product[0]['catid'].'"', 'AND status = 1 AND id != "'.$product[0]['id'].'" ORDER BY rand() LIMIT '.GetTableSet ('MoreProduct'));
			if (count($more) > 0){
				echo '<div class="col-md-12"><div class="container"><div class="row"><br><h2 class="paid-title">منتجات أخرى مشابهه</h2>';//more product
				for ($i=0; $i <= count($more)-1 ; $i++) { 
					GetProduct($more[$i]['id']);
				}
				echo '</div></div></div>';
			}
		}
		//-----------------------------
		echo '</section>';
	}else{
		header('Location: category.php');	exit();
	}
}
//--------------------------------------------------------


