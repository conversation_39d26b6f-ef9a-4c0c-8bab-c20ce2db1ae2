<?php
ob_start();
$Title_page = 'الإعدادات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

	if (isset($_POST['edit1'])){

		Update('Site_Name' , $_POST['var1']);
		Update('Site_URL' , $_POST['var2']);
		Update('Description' , $_POST['var3']);
		Update('Keywords' , $_POST['var4']);
		redirect_home ('back' , 0); exit();
	}	

	if (isset($_POST['edit2'])){

		Update('google_site_verification' , $_POST['var1']);
		Update('Facebook_app_id' , $_POST['var2']);
		Update('Social_Face' , $_POST['var3']);
		Update('Social_twitter' , $_POST['var4']);
		Update('Social_insta' , $_POST['var5']);
		Update('Social_google' , $_POST['var6']);
		Update('Social_youtube' , $_POST['var7']);
		Update('Social_telegram' , $_POST['var8']);
		Update('MesangerCode' , $_POST['var9']);
		redirect_home ('back' , 0); exit();
	}

	
	
	if (isset($_POST['edit3'])){

		Update('FooterDescr' , $_POST['var1']);
		Update('PostsPageTitle' , $_POST['var2']);
		Update('PostsPageDesc' , $_POST['var3']);
		Update('PostsPageKeyWords' , $_POST['var4']);
		Update('AboutPageContent' , $_POST['var5']);
		Update('ShipPageContent' , $_POST['var6']);
		Update('PrivacyPageContent' , $_POST['var7']);
		redirect_home ('back' , 0); exit();
	}

	
	if (isset($_POST['edit4'])){

		Update('Phone' , $_POST['var1']);
		Update('LimitInNavbar' , $_POST['var2']);
		Update('LimitInIndex' , $_POST['var3']);
		Update('LimitInIndexPosts' , $_POST['var4']);
		Update('ProductsPerPage' , $_POST['var5']);
		Update('PostPerPageInPosts' , $_POST['var6']);
		Update('ProductsPerPageInProducts' , $_POST['var7']);
		redirect_home ('back' , 0); exit();
	}

	if (isset($_POST['edit5'])){

		Update('Admin_User' , $_POST['var1']);
		Update('Admin_Pass' , $_POST['var2']);
		redirect_home ('back' , 0); exit();
	}



				?>		
			
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">

			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> الإعدادات العامه</h4><hr>
					<div class="col-md-6">
				<form method="post">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم الموقع</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Site_Name');?>" class="form-control">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">رابط الموقع</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Site_URL');?>" class="form-control ltr">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الوصف</label>
	                      <textarea class="form-control" name="var3"><?php echo GetTableSet ('Description');?></textarea>
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الكلمات الدلاليه</label>
	                      <textarea class="form-control" name="var4"><?php echo GetTableSet ('Keywords');?></textarea>
						</div>

										
	                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit1" class="btn btn-primary">
		                </div>
               	</form> 	
               	 	</div>	
               	 	<div class="col-md-6">
               	 		  <label class="form-control-label" style="text-align: center; width: 100%;">لوجو الموقع</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ;?>" style="width: 200px;height: auto; margin-bottom: 20px;margin-top: 10px;">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photo" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="LOGO">
							        </form>
							        <label for="photo" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploads" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>
							<hr>
							<label class="form-control-label" style="text-align: center; width: 100%;">لوجو 2</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo2') ;?>" style="width: 200px; height: auto;margin-bottom: 20px;margin-top: 10px;">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photo1" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploads1" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="Logo2">
							        </form>
							        <label for="photo1" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploads1" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>  
               	 	</div>	
			</div>	
</div></div></div></div>



<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات أخرى</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">كود اثبات ملكية الموقع لجوجل</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('google_site_verification');?>" class="form-control ltr">
	                    </div>
					</div>	
					
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رقم تطبيق الفيس بوك</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Facebook_app_id');?>" class="form-control ltr">
	                    </div>
					</div>	


					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط الفيس بوك</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('Social_Face');?>" class="form-control ltr">
	                    </div>
					</div>	

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط تويتر</label>
	                      <input type="text" name="var4" value="<?php echo GetTableSet ('Social_twitter');?>" class="form-control ltr">
	                    </div>
					</div>	

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط انستجرام</label>
	                      <input type="text" name="var5" value="<?php echo GetTableSet ('Social_insta');?>" class="form-control ltr">
	                    </div>
					</div>	

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط جوجل</label>
	                      <input type="text" name="var6" value="<?php echo GetTableSet ('Social_google');?>" class="form-control ltr">
	                    </div>
					</div>	

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط اليوتيوب</label>
	                      <input type="text" name="var7" value="<?php echo GetTableSet ('Social_youtube');?>" class="form-control ltr">
	                    </div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط التليجرام</label>
	                      <input type="text" name="var8" value="<?php echo GetTableSet ('Social_telegram');?>" class="form-control ltr">
	                    </div>
					</div>
					
					<div class="col-md-12">
						<div class="form-group">
	                      <label class="form-control-label">كود رسائل الماسنجر</label>
	                      <textarea class="form-control to2 ltr" name="var9"><?php echo GetTableSet ('MesangerCode');?></textarea>
	                    </div>
					</div>
					
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit2" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>


<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات الفوتر والعرض</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">وصف الفوتر</label>
	                      <textarea class="form-control to2" name="var1"><?php echo GetTableSet ('FooterDescr');?></textarea>
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">التايتل فى صفحة المقالات</label>
	                      <textarea class="form-control to2" name="var2"><?php echo GetTableSet ('PostsPageTitle');?></textarea>
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">الوصف فى صفحة المقالات</label>
	                      <textarea class="form-control to2" name="var3"><?php echo GetTableSet ('PostsPageDesc');?></textarea>
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">الكلمات الدلالية فى صفحة المقالات</label>
	                      <textarea class="form-control to2" name="var4"><?php echo GetTableSet ('PostsPageKeyWords');?></textarea>
	                    </div>
					</div>
					
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة معلومات عنا</label>
	                      <textarea class="form-control to2" name="var5"><?php echo GetTableSet ('AboutPageContent');?></textarea>
	                    </div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة الشحن والتوصيل</label>
	                      <textarea class="form-control to2" name="var6"><?php echo GetTableSet ('ShipPageContent');?></textarea>
	                    </div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة سياسة الخصوصية</label>
	                      <textarea class="form-control to2" name="var7"><?php echo GetTableSet ('PrivacyPageContent');?></textarea>
	                    </div>
					</div>


					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit3" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>

<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات المقالات والمنتجات</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رقم الموبايل فى الفوتر</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Phone');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد الاقسام فى الناف بار</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('LimitInNavbar');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد الاقسام الصور فى الرئيسية</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('LimitInIndex');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المقالات فى الرئيسية</label>
	                      <input type="text" name="var4" value="<?php echo GetTableSet ('LimitInIndexPosts');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المنتجات فى الرئيسية</label>
	                      <input type="text" name="var5" value="<?php echo GetTableSet ('ProductsPerPage');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المقالات فى صفحة المقالات</label>
	                      <input type="text" name="var6" value="<?php echo GetTableSet ('PostPerPageInPosts');?>" class="form-control">
	                    </div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المنتجات فى صفحة المنتجات</label>
	                      <input type="text" name="var7" value="<?php echo GetTableSet ('ProductsPerPageInProducts');?>" class="form-control">
	                    </div>
					</div>
					


					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit4" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>


<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> بيانات الإدارة</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">إسم المستخدم</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Admin_User');?>" class="form-control ltr">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">كلمة المرور</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Admin_Pass');?>" class="form-control ltr">
	                    </div>
					</div>
					



					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit5" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>
<?php
include('footer.php'); 
ob_end_flush();
?>