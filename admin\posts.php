<?php
ob_start();
$Title_page = 'المقالات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'posts' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('posts' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('posts' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'posts' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'posts', 'WHERE id = '.$_GET['id'] );
			header('Location: posts.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'posts' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['title'];?> " </b> ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

		     <center>
		     	<a class="btn btn-danger btn-lg" href="posts.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-lg" href="posts.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
	}else{
		header('Location: posts.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة مقالة جديدة </h4>
				</div>
			</div>
			<br>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = $_POST['var3'];  
						$var4  = $_POST['var4'];  
						$var5  = $_POST['var5'];  

						if(empty($var1) || empty($var2) || empty($var3) || empty($var4) ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>';  
						}else{

							$stmt = $db->prepare("INSERT INTO posts ( title , brandid , descr , tags , datee , photo , productid) 
							 VALUES (:user_1 ,:user_2  ,:user_3  ,:user_4  ,:user_5 ,:user_6 ,:user_7 )");  
							$stmt->execute(array(
							  'user_1' => $var1  ,
							  'user_2' => $var2  ,
							  'user_3' => $var3  ,
							  'user_4' => $var4  ,
							  'user_5' => time()  ,
							  'user_6' => 'imgs/Logos/All.png',
							  'user_7' => $var5
							 )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة المقالة بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة المقالات خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=posts.php");
					        exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						
                        <div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">العنوان</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">العلامة التجارية</label>
		                      <select class="form-control" name="var2">
		                      	<option value="0">إختر البراند</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">'.$cc[$i]['ar_name'].' - '.$cc[$i]['en_name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">المنتج</label>
		                      <select class="form-control" name="var5">
		                        <option value="">بدون منتج</option>
		                      	<option value="0">منتج عشوائى</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'products' , 'WHERE status = 1', '');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">'.$cc[$i]['title'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">المحتوى . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var3"></textarea>
							</div>	
	               	 	</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">التاجات او الكلمات الدلالية</label>
								<textarea class="form-control to2" name="var4"></textarea>
							</div>	
	               	 	</div>



	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'posts' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['title'];?>"</h4>
				</div>
			</div><br>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var3  = $_POST['var3'];  
						$var4  = $_POST['var4'];  
						$var5  = $_POST['var5'];  
						$var6  = $_POST['var6'];  
						$var0  = filter_var($_POST['var0']   , FILTER_SANITIZE_STRING) ;
						$var7  = filter_var($_POST['var7']   , FILTER_SANITIZE_STRING) ;
						
						if(empty($var1) || empty($var2)  ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>'; 
					    }else{
						$stmt = $db->prepare("UPDATE posts
						    SET title = :var1  ,
								brandid = :var2  , 
								descr = :var3  , 
								tags = :var4  , 
								datee = :var5  , 
								views = :var6  ,
								productid = :var7  
						    	WHERE  id = :var0 ");  
				           $stmt->execute(array(
                                'var1' => $var1 ,
                                'var2' => $var2 ,
                                'var3' => $var3 ,
                                'var4' => $var4 ,
                                'var5' => strtotime($var5) ,
                                'var6' => $var6 ,
                                'var7' => $var7 ,
                                'var0' => $_GET['id']
				          )); 	
				        echo '<div class="col-md-12">';
				        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
				        echo '</div>';  		 	

				        redirect_home ('back' , 1); exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">العنوان</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['title'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">العلامة التجارية</label>
		                      <select class="form-control" name="var2">
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC , id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
										if ($ch[0]['brandid']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;}  
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>'.$cc[$i]['ar_name'].' - '.$cc[$i]['en_name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
                        <div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">المنتج</label>
		                      <select class="form-control" name="var7">
                              <option value="0">منتج عشوائى</option>
                              <option value="">بدون منتج</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'products' , 'WHERE status = 1', 'ORDER BY id DESC');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
										if ($ch[0]['productid']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;}  
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>'.$cc[$i]['title'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">المحتوى . يمكن استخدام (html)</label>
								<textarea class="form-control to2" name="var3"><?php echo $ch[0]['descr'];?></textarea>
							</div>	
	               	 	</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label class="form-control-label">التاجات او الكلمات الدلالية</label>
								<textarea class="form-control to2" name="var4"><?php echo $ch[0]['tags'];?></textarea>
							</div>	
	               	 	</div>

						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">التاريخ</label>
		                      <input type="text" name="var5" value="<?php echo date("Y/m/d H:i" , $ch[0]['datee']);?>" class="form-control">
		                    </div>
	               	 	</div>	

						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">عدد المشاهدات</label>
		                      <input type="number" name="var6" value="<?php echo $ch[0]['views'];?>" class="form-control">
		                    </div>
	               	 	</div>	

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>

				<?php
					
						echo '<div class="divs"><br><hr>';
						echo '<h4><i class="fa fa-picture-o" aria-hidden="true"></i> صورة المقالة</h4><p>800*800</p>';
						$ph = 'imgs/Logos/All.png' ;
						if (!empty($ch[0]['photo'])){
							$ph = $ch[0]['photo'];
						}
						?>
						<center>
					    <img style="margin: 20px 0px; width: 200px; max-width: 100%;" src="<?php echo $Site_URL.'/'.$ph ;?>">
					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
					          <input type="file" name="photo" id="photo" required style="display: none;" />
					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
					          <input type="hidden" name="Image_For" value="post_photo">
					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">
					    </form>
					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>
					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
					  </center>

						<?php
						echo '</div>';
					
				?>
			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: posts.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="posts.php?do=add_new" class="btn btn-lg btn-success">إضافة مقالة جديدة </a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all">Post ID</th>
								<th class="all">العنوان</th>
								<th class="all">العلامة التجارية</th>
                            	<th class="mobile-p desktop">المشاهدات</th>
                            	<th class="none">التاريخ</th>
                            	<th class="mobile-p desktop">الحالة</th>
                            	<th class="mobile-p desktop">الإجراء</th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'posts'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [50, 100, 200, 500, 1000, -1],
                  [50, 100, 200, 500, 1000, "كل  المقالات"]
                ],
                'destroy': true,
                responsive:true,
                "order": [[ 0, "desc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن مقالة",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });
</script>