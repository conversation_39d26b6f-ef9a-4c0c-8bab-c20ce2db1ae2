<?php
    if (!isset($_SESSION['userData'])){header("Location:".$Site_URL.'/admin/login.php'); exit();}
?>
<body>
    <!-- Static navbar -->
    <nav class="navbar navbar-default yamm navbar-fixed-top">
        <div class="container-fluid">
            <button type="button" class="navbar-minimalize minimalize-styl-2  pull-left "><i class="fa fa-bars"></i></button>
            <div class="navbar-header">
                <a class="navbar-brand" href="index.php">لوحة التحكم</a>
            </div>
        </div><!--/.container-fluid -->
    </nav>
    <section class="page">

        <nav class="navbar-aside navbar-static-side" role="navigation">
            <div class="sidebar-collapse nano">
                <div class="nano-content">
                    <ul class="nav metismenu" id="side-menu">
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الرئيسية'){echo 'active';}?>">
                            <a href="index.php"><i class="fa fa-laptop"></i> <span class="nav-label">الرئيسية</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'المنتجات'){echo 'active';}?>">
                            <a href="products.php"><i class="fa fa-shopping-cart"></i> <span class="nav-label">المنتجات</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'المقالات'){echo 'active';}?>">
                            <a href="posts.php"><i class="fa fa-file-image-o"></i> <span class="nav-label">المقالات</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'العلامات التجارية'){echo 'active';}?>">
                            <a href="brands.php"><i class="fa fa-connectdevelop"></i> <span class="nav-label">العلامات التجارية</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'القدرات'){echo 'active';}?>">
                            <a href="power.php"><i class="fa fa-bolt"></i> <span class="nav-label">القدرات</span></a>
                        </li>

                        <li class="<?php if(isset($Title_page) && $Title_page == 'الأنواع'){echo 'active';}?>">
                            <a href="kinds.php"><i class="fa fa-th-large"></i> <span class="nav-label">الأنواع</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الكلمات الدلالية'){echo 'active';}?>">
                            <a href="tags.php"><i class="fa fa-tags"></i> <span class="nav-label">الكلمات الدلالية</span></a>
                        </li>
                        
                        <li class="<?php if(isset($Title_page) && $Title_page == 'ارقام الموبايلات'){echo 'active';}?>">
                            <a href="phones.php"><i class="fa fa-phone"></i> <span class="nav-label">ارقام الموبايلات</span></a>
                        </li>
                        
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الإعدادات'){echo 'active';}?>">
                            <a href="settings.php"><i class="fa fa-cogs"></i> <span class="nav-label">الإعدادات</span></a>
                        </li>

                        <li class="<?php if(isset($Title_page) && $Title_page == 'إتصل بنا'){echo 'active';}?>">
                            <a href="contactus.php"><i class="fa fa-commenting-o"></i> <span class="nav-label">إتصل بنا</span></a>
                        </li>

                        <li class="<?php if(isset($Title_page) && $Title_page == 'الرجوع الى الموقع'){echo 'active';}?>">
                            <a href="<?php echo $Site_URL;?>/"><i class="fa fa-retweet"></i> <span class="nav-label">الرجوع الى الموقع</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'تسجيل الخروج'){echo 'active';}?>">
                            <a href="<?php echo $Site_URL;?>/logout.php"><i class="fa fa-sign-out"></i> <span class="nav-label">تسجيل الخروج</span></a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <div id="wrapper">
            <div class="content-wrapper container">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="page-title">
                            <h1><?php if (isset($Title_page)){echo $Title_page ; }else{echo getTitle();}?> <small></small></h1>
                            <ol class="breadcrumb">
                                <li><a href="index.php"><i class="fa fa-home"></i></a></li>
                                <li class="active"><?php if (isset($Title_page)){echo $Title_page ; }else{echo getTitle();}?></li>
                            </ol>
                        </div>
                        <hr>
                    </div>
                </div><!-- end .page title-->