<?php
ob_start();
$Title_page = 'الأنواع';
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php'); 

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('kinds' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('kinds' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] >= 0){
			UpdateTable('kinds' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] > 0){
			UpdateTable('kinds' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'kinds', 'WHERE id = '.$_GET['id'] );
			header('Location: kinds.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
			     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['name'];?> " </b> ؟</h3>
			     <p>برجاء العلم انه سيتم الحذف بشكل نهائي ولا يمكن الرجوع فيه.</p>

			     <center>
			     	<a class="btn btn-danger btn-md" href="kinds.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-md" href="kinds.php">رجوع</a>
			     </center>
		 
			</div>	
		</div>
	</div>
</div>
<?php
	}else{
		header('Location: kinds.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'kinds' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel">التعديل على <i class="fa fa-hand-o-left"></i><b> <?php echo $ch[0]['name'];?></b> </h4>
				</div>
			</div>
		</div>			
	</div>		
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
	                 	$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						
                         if (empty($var1) || empty($var2)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول.');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("UPDATE kinds
							    SET name = :var1  ,
                                en = :var2  
							    	WHERE  id = :var0 ");  
					        $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var0' => $_GET['id']
					        )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

					        redirect_home ('back' , 1); exit();
					    }
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم بالعربى</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['name'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم بالانجليزى</label>
		                      <input type="text" name="var2" value="<?php echo $ch[0]['en'];?>" class="form-control ltr">
		                    </div>
	               	 	</div>
						
	               	 	
		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                      <a href="kinds.php" class="btn btn-lg btn-primary">رجوع</a>
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>


			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: kinds.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة نوع جديد </h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
	                 	$var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						
						if (empty($var1) || empty($var2)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول.');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO kinds ( name , en , logo ) 
							 VALUES (:user_1 ,:user_2 ,:user_3 )");  
							$stmt->execute(array(
					          'user_1' => $var1 , 'user_2' =>  $var2  , 'user_3' => 'imgs/Logos/All.png'  )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة نوع جديد بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الانواع خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=kinds.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label"> الأسم بالعربى</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم بالانجليزى</label>
		                      <input type="text" name="var2" value="" class="form-control ltr">
		                    </div>
	               	 	</div>

	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="kinds.php?do=add_new" class="btn btn-lg btn-success">إضافة نوع جديد</a>
			</div>
		</div>
	</div>			
</div>


	<?php
	$check = getAllFrom('*' , 'kinds' , '', 'ORDER BY orders DESC ,id DESC');
	if(count($check) > 0){
		
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if ($check[$i]['status'] == 0 ){
				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
			}else{
				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
			}
			echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card"><hr>
			<div class="card-body">';
			echo '<div class="divs">';
			echo '<h4 class="cattitlel">'.$check[$i]['orders'].' - '.$check[$i]['name'].'</h4>';
			echo ' <a href="kinds.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary">تعديل</a> ';
			echo ' <a href="kinds.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
			echo ' <a href="kinds.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '<a href="kinds.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
                        	<a href="kinds.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>';
			echo '</div>';
			
			
			echo '</div></div></div></div>';
			
		}
	}else{
		echo  '<br>'.Show_Alert('warning' , 'لا يوجد أنواع. ');
	}	

}
?>
<?php
include('footer.php'); 
ob_end_flush();
?>