<?php
ob_start();
$Title_page = 'الكلمات الدلالية' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'tags' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('tags' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('tags' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'tags' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'tags', 'WHERE id = '.$_GET['id'] );
			header('Location: tags.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'tags' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['name'];?> " </b> ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

		     <center>
		     	<a class="btn btn-danger btn-lg" href="tags.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-lg" href="tags.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
	}else{
		header('Location: tags.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة كلمة دلالية جديدة </h4>
				</div>
			</div>
			<br>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
                        $var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;

						if(empty($var1) || empty($var2) ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>';  
						}else{

							$stmt = $db->prepare("INSERT INTO tags ( name , count) 
							 VALUES (:user_1 ,:user_2 )");  
							$stmt->execute(array(
					          'user_1' => $var1  , 'user_2' => $var2 )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة الكلمة بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الكلمات الدلالية خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=tags.php");
					        exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						
                        <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الكلمة الدلالية</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">عدد المرات</label>
		                      <input type="number" name="var2" value="0" class="form-control">
		                    </div>
	               	 	</div>    

	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'tags' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['name'];?>"</h4>
				</div>
			</div><br>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
                        $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
                        $var2  = trim(filter_var($_POST['var2']   , FILTER_SANITIZE_STRING)) ;
						$var0  = filter_var($_POST['var0']   , FILTER_SANITIZE_STRING) ;
						
						if(empty($var1) || empty($var2)  ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>'; 
					    }else{
						$stmt = $db->prepare("UPDATE tags
						    SET name = :var1  ,
                                count = :var2   
						    	WHERE  id = :var0 ");  
				           $stmt->execute(array(
                                'var1' => $var1 ,
                                'var2' => $var2 ,
                                'var0' => $_GET['id']
				          )); 	
				        echo '<div class="col-md-12">';
				        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
				        echo '</div>';  		 	

				        redirect_home ('back' , 1); exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الكلمة الدلالية</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['name'];?>" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">عدد المرات</label>
		                      <input type="number" name="var2" value="<?php echo $ch[0]['count'];?>" class="form-control">
		                    </div>
	               	 	</div>


		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: tags.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="tags.php?do=add_new" class="btn btn-lg btn-success">إضافة كلمة دلالية جديدة </a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all">الكلمة</th>
								<th class="all">عدد المرات</th>
                            	<th class="mobile-p desktop">الحالة</th>
                            	<th class="mobile-p desktop">الإجراء</th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'tags'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [50, 100, 200, 500, 1000, -1],
                  [50, 100, 200, 500, 1000, "كل  الكلمات"]
                ],
                'destroy': true,
                responsive:true,
                "order": [[ 1, "desc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن كلمة",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });
</script>