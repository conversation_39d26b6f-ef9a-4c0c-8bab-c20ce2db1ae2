<?php
$Mcats = getAllFrom('*' , 'brands' , 'WHERE status = 1', 'ORDER BY orders DESC, id DESC');
?>
<style>
.winter-is-coming, .snow {
  z-index: 100;
  pointer-events: none;
}

/*.winter-is-coming {*/
/*  overflow: hidden;*/
/*  position: absolute;*/
/*  top: 0;*/
/*  height: 100%;*/
/*  width: 100%;*/
/*  max-width: 100%;*/
/*}*/

/*.snow {*/
/*  position: absolute;*/
/*  top: 0;*/
/*  right: 0;*/
/*  bottom: 0;*/
/*  left: 0;*/
/*  -webkit-animation: falling linear infinite both;*/
/*          animation: falling linear infinite both;*/
/*  -webkit-transform: translate3D(0, -100%, 0);*/
/*          transform: translate3D(0, -100%, 0);*/
/*}*/
/*.snow--near {*/
/*  -webkit-animation-duration: 10s;*/
/*          animation-duration: 10s;*/
/*  background-image: url("https://dl6rt3mwcjzxg.cloudfront.net/assets/snow/snow-large-075d267ecbc42e3564c8ed43516dd557.png");*/
/*  background-size: contain;*/
/*}*/
/*.snow--near + .snow--alt {*/
/*  -webkit-animation-delay: 5s;*/
/*          animation-delay: 5s;*/
/*}*/
/*.snow--mid {*/
/*  -webkit-animation-duration: 20s;*/
/*          animation-duration: 20s;*/
/*  background-image: url("https://dl6rt3mwcjzxg.cloudfront.net/assets/snow/snow-medium-0b8a5e0732315b68e1f54185be7a1ad9.png");*/
/*  background-size: contain;*/
/*}*/
/*.snow--mid + .snow--alt {*/
/*  -webkit-animation-delay: 10s;*/
/*          animation-delay: 10s;*/
/*}*/
/*.snow--far {*/
/*  -webkit-animation-duration: 30s;*/
/*          animation-duration: 30s;*/
/*  background-image: url("https://dl6rt3mwcjzxg.cloudfront.net/assets/snow/snow-small-1ecd03b1fce08c24e064ff8c0a72c519.png");*/
/*  background-size: contain;*/
/*}*/
/*.snow--far + .snow--alt {*/
/*  -webkit-animation-delay: 15s;*/
/*          animation-delay: 15s;*/
/*}*/

/*@-webkit-keyframes falling {*/
/*  0% {*/
/*    -webkit-transform: translate3D(-7.5%, -100%, 0);*/
/*            transform: translate3D(-7.5%, -100%, 0);*/
/*  }*/
/*  100% {*/
/*    -webkit-transform: translate3D(7.5%, 100%, 0);*/
/*            transform: translate3D(7.5%, 100%, 0);*/
/*  }*/
/*}*/

/*@keyframes falling {*/
/*  0% {*/
/*    -webkit-transform: translate3D(-7.5%, -100%, 0);*/
/*            transform: translate3D(-7.5%, -100%, 0);*/
/*  }*/
/*  100% {*/
/*    -webkit-transform: translate3D(7.5%, 100%, 0);*/
/*            transform: translate3D(7.5%, 100%, 0);*/
/*  }*/
/*}*/

.btn-white {
    border-color: rgba(0,0,0,0.2);
    text-align: center;
    width: 80px;
    height: 80px;
    padding: 5px;
    background: #ffF;
    box-shadow: 0px 0px 3px #000;
    margin: auto;
}

.btn-white i {
    color: #4290ce;
}
.arrowdown {
    position: absolute;
    left: 14px;
    color: #CCC!important;
    top: 12px;
}
.topnav{
    margin-top: 30px;
}
.btn-white img{
    width:100%;
    height:100%;
}
.modal-body a{
    display:block;
    margin:5px;
    
}
body {
    width: 100%;
    max-width: 100%;
}
</style>




<header><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 










<div class="top-header">
    <div class="winter-is-coming">
  <div class="snow snow--near"></div>
  <div class="snow snow--near snow--alt"></div>
  
  <div class="snow snow--mid"></div>
  <div class="snow snow--mid snow--alt"></div>
  
  <div class="snow snow--far"></div>
  <div class="snow snow--far snow--alt"></div>
</div>
   
        <div class="container">
          <div class="col-md-12">
            <div class="navbar-mobile" onclick="ShowMobileNav()">
              <button type="button" class="navbar-toggle" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
            </div>
            <center>
                      
            <div class="logo"> <a title="<?php echo $Site_Name ; ?>" href="<?php echo $Site_URL;?>"><img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ;?>" alt="<?php echo $Site_Name ; ?>" ></a> </div>
            <div class="top-brands">
	   				 <a href="Search.php?AirConditioningBrand=Sharp">  <img alt="تكييف شارب" src="img/logos/Sharp.png">  </a>  
	   				 <a href="Search.php?typeahead=تكييف+كاريير">  <img alt="تكييف كاريير" src="img/logos/Carrier.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=UnionAir">  <img alt="تكييف يونيون اير" src="img/logos/UnionAir.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=Gree">  <img alt="تكييف جري" src="img/logos/Gree.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=Fresh">  <img alt="تكييف فريش" src="img/logos/Fresh.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=Midea">  <img alt="تكييف ميديا" src="img/logos/Midea.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=LG">  <img alt="تكييف ال جي" src="img/logos/LG.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=Samsung">  <img alt="تكييف سامسونج" src="img/logos/Samsung.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=Tornado">  <img alt="تكييف تورنيدو" src="img/logos/Tornado.png">  </a>  
	   				 <a href="Search.php?AirConditioningBrand=WhiteWhale">  <img alt="وايت ويل" src="img/logos/WhiteWhale.png">  </a>  
				</div>
				
							
				
     
          </center>
          </div>
          <!--<div class="col-md-9 col-md-offset-1">-->
          <!--    <div class="header-search">-->
          <!--      <form method="get" action="<?php echo $Site_URL;?>/Search.php">-->
          <!--      <div class="input-group">-->
                  
          <!--        <input style="width: 100% !important;" type="text" class="form-control inbmob" id="search-title" name="typeahead" placeholder="إبحث فى <?php echo $Site_Name ;?>" value="<?php if (isset($_GET['typeahead'])){echo $_GET['typeahead'];}?>" aria-describedby="basic-addon1">-->

          <!--        <span class="input-group-addon" id="basic-addon1"><button class="search-btn-h" type="submit"><i class="fa fa-search search-btn-hide"></i><span class="hide-lg">بحث<span></button></span>-->
          <!--      </div>-->
          <!--      </form>-->
          <!--    </div>-->
          <!--</div>-->
          
          
          
        </div>
        <div class="mobile-nav">
            <div class="mlogo"> <a title="<?php echo $Site_Name ; ?>" href="<?php echo $Site_URL;?>"><img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo2') ;?>" alt="<?php echo $Site_Name ; ?>" ></a> </div>
            <a onclick="ShowMobileNav()" class="close-nav">x</a>
            <ul class="nav navbar-nav">
            <li><a href="<?php echo $Site_URL;?>">الرئيسية</a></li>
            <?php
                if (count($Mcats) > 0 ){
                    for ($i=0; $i <= count($Mcats)-1 ; $i++) { 
                      echo '<li><a href="'.$Site_URL.'/Search.php?AirConditioningBrand='.$Mcats[$i]['ar_name'].'">'.$Mcats[$i]['ar_name'].'</a></li>'; 
                    }
                }
            ?>
          </ul>
        </div>
    </div>
    <nav class="navbar navbar-default">
      <div class="container">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
          <ul class="nav navbar-nav">
            <li><a href="<?php echo $Site_URL;?>">الرئيسية</a></li>
            <?php
            
            $cats = getAllFrom('*' , 'brands' , 'WHERE status = 1 ', 'ORDER BY orders DESC, id DESC LIMIT '.GetTableSet ('LimitInNavbar'));
                if (count($cats) > 0 ){
                    for ($i=0; $i <= count($cats)-1 ; $i++) { 
                      echo '<li><a href="'.$Site_URL.'/Search.php?AirConditioningBrand='.$cats[$i]['ar_name'].'">'.$cats[$i]['ar_name'].'</a></li>'; 
                    }
                }
                
            ?>
          </ul>
        </div><!-- /.navbar-collapse -->
      </div><!-- /.container-fluid -->
    </nav>
    
    
    <style>
        .bigbox{
            width: 100%;
            height: 70px;
            margin: auto;
            display: flex;
            background: #fff;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 10px;
            max-width: 500px;
        }
        .big-icon , .big-btn , .big-content{
            width:100%;
            display:inline-block;
        }
        .big-icon img{
            width: 50px;                                    
            height: 50px;
        }
        .big-content h2{
            margin: 0;
            padding: 0;
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: bold;
            margin-top: 5px;
        }
        .big-content p{
            
        }
        .big-btn a{
            padding: 10px 15px;
            border: 1px solid #ccc;
            border-radius: 20px;
        }
        .bigbad{
            padding:5px;
        }
        .hidedt{
            display:none;
        }
        @media (max-width: 767px){
            .hidedt{
                display:block;
            }
        }
    </style>
    
    
    <!--<div class="col-md-4 hidedt">-->
    <!--    <div class="bigbox">-->
    <!--        <div class="col-xs-3 bigbad">-->
    <!--            <div class="big-icon text-center">-->
    <!--                <img src="icon-hors.png">-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        <div class="col-xs-9 bigbad">-->
    <!--            <div class="big-content">-->
    <!--                <h2>عايز كام حصان ؟</h2>-->
    <!--                <p>اضغط هنا اختيار القدرة</p>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </div>-->
    <!--</div>-->
    <!--<div class="col-md-4 hidedt">-->
    <!--    <div class="bigbox">-->
    <!--        <div class="col-xs-3 bigbad">-->
    <!--            <div class="big-icon text-center">-->
    <!--                <img src="icon-brand.png">-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        <div class="col-xs-9 bigbad">-->
    <!--            <div class="big-content">-->
    <!--                <h2>أختار الماركة</h2>-->
    <!--                <p>أختار ماركة التكييف وشوف العروض</p>-->
    <!--            </div>-->
    <!--        </div>-->
            
    <!--    </div>-->
    <!--</div>-->
    
</header>

<div id="PhoneData" class="modal fade"  role="dialog">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <div class="modal-body">
          <div class="PhoneDataClose">
            <a data-dismiss="modal"><i class="fa fa-window-close" aria-hidden="true"></i></a>
          </div>
        <div class="logo"><img src="https://sevencool.co/mac1.png"></div>
          <div id="PhoneDataRes">
              <h3>لمعرفة العروض المتاحه الان كاش وتقسيط تواصل معنا</h3>
              <h5>أضعط على الارقام للاتصال</h5>

              <?php
                  $tbphone = getAllFrom('*' , 'phones' , 'WHERE status = 1 ', 'ORDER BY orders DESC, id DESC ');
                  echo '<ul>';
                  for ($i=0; $i <= count($tbphone)-1 ; $i++) { 
                      echo '<li><a href="tel:'.$tbphone[$i]['phone'].'"><i class="fa fa-mobile" aria-hidden="true"></i> '.$tbphone[$i]['phone'].'</a></li>';
                  }
                  echo '</ul>';
              ?>
          </div>  
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="mod1" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">إختر الماركة</h5>
      </div>
      <div class="modal-body">
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="mod2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">إختر الماركة</h5>
      </div>
      <div class="modal-body">
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
       <a href="#">شارب العربي</a>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>


<div class="main-content">
