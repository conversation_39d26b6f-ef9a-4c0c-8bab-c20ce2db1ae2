@media (min-width: 992px){
	.col-md-25 {
		text-align: center;
	    width: 25%;
	    position: relative;
	    min-height: 1px;
	    float: right;
	}
}
@media (min-width: 1200px){
	.col-md-25 {
		text-align: center;
	    width: 20%;
	    position: relative;
	    min-height: 1px;
	    float: right;
	}
}
@media (max-width: 767px){

}
@media (max-width: 767px){
	.col-md-25 {
		text-align: center;
	    width: 33.3333%;
	    position: relative;
	    min-height: 1px;
	    float: right;
	}
	.logo img{
		width: 200px;
	}
	.mlogo{
		display: inline-block;
		padding: 10px 10px 10px 0px;
	}
	.mlogo img{
		width: 150px;
	}
	.mobile-nav{
		position: fixed;
		top: 0px;
		right: 0px;
		background: #333A3D;
		color: #fff;
		width: 300px;
		height: 100%;
		z-index: 1000;
	}
	.mobile-nav .navbar-nav {
	    margin: 0px;
	}
	.mobile-nav a{
		text-decoration: none !important;
	} 
	.mobile-nav .nav>li>a{
		color: #fff;
		font-size: 16px;
		padding: 15px 20px;
	}
	.mobile-nav .nav>li>a:focus, .mobile-nav .nav>li>a:hover {
	    text-decoration: none;
	    color: #92c7ff;
	    background-color: rgba(0, 0, 0, 0.3);
	}
	.header-search .form-control {
	    min-height: 32px;
	}
	.mobile-nav .navbar-nav .open .dropdown-menu>li>a,.mobile-nav .navbar-nav .open .dropdown-menu .dropdown-header {
    	padding: 15px 25px;
    	color: #ddd;
	}
	.mobile-nav .caret {
	    margin-right: 2px;
	    margin-left: 0;
	    float: left;
	    display: inline-block;
	    margin-top: 7px;
	}
	.close-nav {
	    float: left;
	    display: block;
	    color: #b3b3b3;
	    font-size: 20px;
	    width: 30px;
	    height: 30px;
	    overflow: hidden;
	    font-weight: bold;
	    border-radius: 50%;
	    background: #242829;
	    text-align: center;
	    line-height: 25px;
	    padding: 0;
	    border: 1px solid #464f52;
	    margin: 10px;
	    cursor: pointer;
	    display: inline-block;
	}
	.close-nav:hover , .close-nav:focus , .close-nav:active{
		color: #fff;
	}
	.navbar-mobile .icon-bar {
	    display: block;
	    width: 25px;
	    height: 4px;
	    border-radius: 2px;
	    background: #333;
	}
	.navbar-mobile .navbar-toggle{
		float: right;
		margin: 0;
		padding: 0;
		margin-top: 12px;
		margin-left: 5px;
	}
	.header-search {
	    padding: 5px 0px 0px 0px;
	    margin-top: 10px;
	}
	.navbar{
		display: none;
	}
	.header-search .input-group-addon {
	    padding: 3px;
	    font-size: 14px;
	    font-weight: 400;
	    line-height: 1;
	    color: #555;
	    text-align: center;
	    background-color: #eee;
	    border: 0px solid #c5c5c5;
	    border-radius: 5px;
	    border-bottom-right-radius: 0;
	    border-top-right-radius: 0;
	    border-right: 0px;
	}
	.search-btn-h {
		background: #EEEEEE;
		border: 0;
		font-size: 14px;
		color: #444444;
	    padding: 2px 7px;
	    margin: 3px auto;
	}
	.search-btn-hide{
		display: none;
	}
	.inbmob{
	  display: block !important;
	  width: 100% !important;
	}
	.hide-lg{
		display: inline-block;
	}
	.navbar-default .navbar-nav .open .dropdown-menu>li>a {
	    color: #fff;
	    cursor: pointer;
	}
	.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
	    color: #ffffff;
    	background-color: rgba(255, 255, 255, 0.1);
	}
	.navbar-nav .open .dropdown-menu>li>a, .navbar-nav .open .dropdown-menu .dropdown-header {
	    padding: 8px 25px 8px 15px;
	}
	.hide-sm{
		display: none !important;
	}
	.nav-m{
		display: inline-block !important;
	}
	.slick-slider{
		height: 100% !important;
	}
	.pad0inmob{
		padding: 1px !important;
	}
	.Modern-Slider .item h5{
	  margin-right: 5px;
	}
	footer h4 {
	    margin-top: 20px;
	}
	.owl-item{
	  width: 180px;
	}
	.product article img {
    	height: 160px;
	}
}


@media (max-width: 480px){
	.col-md-25 {
		text-align: center;
	    width: 50%;
	    position: relative;
	    min-height: 1px;
	    float: right;
	}
	.nav-title {
    	font-size: 12px;
    }
	section.carousel {
	    height: 200px;
	}
	.owl-item{
	  width: 96%;
	  margin : 5px;
	}
	.product article img {
    	height: 130px;
	}
	.product article .sale-tag {
	    background: #ee1f38;
	    font-size: 10px;
	    padding: 0px 3px;
	}
	.product article p.rev {
	    font-size: 8px;
	}
	.category-section .cats {
    	width: 150px;
	}
	.category-section .cats p {
	    margin: 10px 0px 0px 0px;
	}
	.sideimg{
	  padding-right: 0px !important;
	}
}