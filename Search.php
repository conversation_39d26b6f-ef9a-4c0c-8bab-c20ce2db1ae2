<?php
ob_start();
$Title_page = 'نتائج البحث' ;
include('webset.php');
include('session.php');
$totalpage = 0;
$dtmod = time();

if(isset($_GET['key'])){
	$key  = filter_var($_GET['key'], FILTER_SANITIZE_STRING) ;
	 $allTags = getAllFrom('name' , 'tags' , 'WHERE status = 1 AND name LIKE "%'.$key.'%" ' , 'ORDER BY count DESC ,id ASC');
	 $ret= array();
	  if (count($allTags) > 0){
		  for ($i=0; $i <= count($allTags)-1 ; $i++) { 
			  array_push($ret , $allTags[$i]['name']);
		  }
			echo json_encode($ret);
	  }	
	exit;  
}


//--------------------------------------------------------------------------------------
$PageTit1 = ''; $PageTit2 = ''; $PageTit3 = ''; $PageTit0 = '';
$PageImg1 = ''; $PageImg2 = ''; $PageImg3 = ''; $PageImg0 = '';
$ennameX = "" ;
//--------------------------------------------------------------------------------------
if (isset($_GET['Brand']) || isset($_GET['power']) || isset($_GET['kind']) ){

	if (isset($_GET['Brand'])){ $brandx = filter_var($_GET['Brand'] , FILTER_SANITIZE_STRING); }
	if (isset($_GET['power'])){ $powerx = filter_var($_GET['power'] , FILTER_SANITIZE_STRING); }
	if (isset($_GET['kind'])){ $kindx = filter_var($_GET['kind'] , FILTER_SANITIZE_STRING); }
	
	$sh1 = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND ( ar_name = "'.$brandx.'" OR  en_name = "'.$brandx.'" ) ' , 'ORDER BY id DESC');
	if (count($sh1) > 0){
		$NavBrandId = $sh1[0]['id'];
		$PageTit1 = str_replace('تكييف' , '' , $sh1[0]['ar_name']);
		$PageImg1 = $sh1[0]['fb_logo'];
		$ennameX  = $sh1[0]['en_name'];
	}
	$sh2 = getAllFrom('*' , 'power' , 'WHERE status = 1 AND  powerint = "'.$powerx.'" ' , 'ORDER BY id DESC');
	if (count($sh2) > 0){
		$NavPowerId = $sh2[0]['id'];
		$PageTit2 = str_replace('تكييف' , '' , $sh2[0]['name']);
		$PageImg2 = $sh2[0]['logo'];
	}
	$sh3 = getAllFrom('*' , 'kinds' , 'WHERE status = 1 AND ( name = "'.$kindx.'" OR  en = "'.$kindx.'" ) ' , 'ORDER BY id DESC');
	if (count($sh3) > 0){
		$NavKindsId = $sh3[0]['id'];
		$PageTit3 = str_replace('تكييف' , '' , $sh3[0]['name']);
		$PageImg3 = $sh3[0]['logo'];
	}
//--------------------------------------------------------------------------------------
}elseif ( (isset($_GET['typeahead']) && trim($_GET['typeahead']) != "") || (isset($_GET['AirConditioningBrand']) && trim($_GET['AirConditioningBrand']) != "") ){
	$NavBrandId = '0' ; $NavPowerId = '0' ; $NavKindsId = '0';

	if (isset($_GET['typeahead']) ){
		$typeahead = filter_var($_GET['typeahead'] , FILTER_SANITIZE_STRING);
	}elseif(isset($_GET['AirConditioningBrand'])){
		$typeahead = filter_var($_GET['AirConditioningBrand'] , FILTER_SANITIZE_STRING);
	}else{
		$typeahead = '';
	}

    $descr = ''; 
    $hidecontent = "";
	$typeahead = trim(str_replace('تكييف' , '' , $typeahead));
    $typeahead = trim(str_replace('+' , ' ' , $typeahead));

	$NavTypeahead = $typeahead ;
	$parts = explode(' ' , $typeahead);
	//if (count($parts) > 0){
	//	for ($l=0; $l <= count($parts)-1; $l++) { 
	//		$ch = trim($parts[$l]);
	$ch = $NavTypeahead;
			if ($ch != ''){
				$sh1 = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND ( ar_name ="'.$ch.'" OR ar_name ="تكييف '.$ch.'" OR  en_name = "'.$ch.'" ) ' , 'ORDER BY id DESC');
				//print_r($sh1);
				if (count($sh1) > 0){
				    
					$NavBrandId = $sh1[0]['id'];
					$PageTit1 = str_replace('تكييف' , '' , $sh1[0]['ar_name']);
					$PageImg1 = $sh1[0]['fb_logo'];
					$descr = $sh1[0]['fulldescr'];
					$hidecontent = $sh1[0]['hidecontent'];
					$ennameX = $sh1[0]['en_name'];
					
					if($descr != ""){
					    
					    $h1 = 'اسعار تكييف '.$PageTit1.' 2025 '.$ennameX .' خصومات وعروض تكييفات '.$PageTit1.'';
					    $pds = 'تعرف على اسعار تكييف '.$PageTit1.' 2025 الموقع الافضل للتكييفات في عروض التكييف سعر تكييف '.$PageTit1.' خصومات وجميع تفاصيل تكييفات '.$PageTit1.'';
					    $dtmod = $sh1[0]['dateModified'];
					    echo '
				
					    
					    ';
					}
					
					
				}
				$sh2 = getAllFrom('*' , 'power' , 'WHERE status = 1 AND  powerint = "'.$ch.'" ' , 'ORDER BY id DESC');
				if (count($sh2) > 0){
					$NavPowerId = $sh2[0]['id'];
					$PageTit2 = str_replace('تكييف' , '' , $sh2[0]['name']);
					$PageImg2 = $sh2[0]['logo'];
				}
				$sh3 = getAllFrom('*' , 'kinds' , 'WHERE status = 1 AND ( name LIKE "%'.$ch.'" OR  en LIKE "%'.$ch.'" ) ' , 'ORDER BY id DESC');
				if (count($sh3) > 0){
					$NavKindsId = $sh3[0]['id'];
					$PageTit3 = str_replace('تكييف' , '' , $sh3[0]['name']);
					$PageImg3 = $sh3[0]['logo'];
				}
			}
	//	}
//	}


	if ($NavBrandId == '0' && $NavPowerId == '0' && $NavKindsId == '0'){
	    $NavTypeahead = str_replace("تكييف" , "" , $NavTypeahead);
        
		$testproducts = getAllFrom('*' , 'products' , 'WHERE status = 1 AND (title LIKE "%'.$NavTypeahead.'%"  OR descr LIKE "%'.$NavTypeahead.'%" OR tags LIKE "%'.$NavTypeahead.'%") ', 'ORDER BY powerid ASC LIMIT 1 ');
		if(count($testproducts) > 0){
			$NavBrandId = $testproducts[0]['brandid'];
			$NavPowerId = $testproducts[0]['powerid'];
			$NavKindsId = $testproducts[0]['kindid'];
			$vsh1x = getAllFrom('*' , 'brands' , 'WHERE status = 1 AND id = "'.$NavBrandId.'"', '');
        	if (count($vsh1x) > 0){
				$PageImg0 = $vsh1x[0]['fb_logo'];
			}
		}
	}

	if ($PageTit1 != ''){
		$PageTit0 = $PageTit1;
		$PageImg0 = $PageImg1;
	}else if($PageTit3 != ''){
		$PageTit0 = $PageTit3;
		$PageImg0 = $PageImg3;
	}else if($PageTit2 != ''){
		$PageTit0 = $PageTit2;	
		$PageImg0 = $PageImg2;
	}
	$PageTit0 = trim($PageTit0);
	
	if ($PageTit0 == '' && isset($_GET['typeahead']) ){
		$PageTit0 = $NavTypeahead;
		if($PageImg0 == ''){
			$PageImg0 = GetTableSet ('Logo2');
		}
	}
	
	
	//$Title_page = 'اسعار تكييف '.$PageTit0.' 2025 خصومات ، وارقام توكيل تكييفات '.$PageTit0.'' ;
	$Title_page  = 'اسعار تكييف '.$PageTit0.' 2025 '.$ennameX .' خصومات وعروض تكييفات '.$PageTit0.'';
	$Page_Description = 'تعرف على اسعار تكييف '.$PageTit0.' 2025 الموقع الافضل للتكييفات في عروض التكييف سعر تكييف '.$PageTit0.' خصومات وجميع تفاصيل تكييفات '.$PageTit0.'';
	$Page_images =  $Site_URL.'/'.$PageImg0 ;
	
	include('header.php');
	include('navbar.php');
	$shpbrand = ''; $shpower = ''; 
	if ($NavBrandId != '0'){
		$shpbrand = ' AND brandid = '.$NavBrandId;
	}
	if ($NavPowerId != '0'){
		$shpower = ' AND powerid = '.$NavPowerId;
	}
	
	if ($descr != ""){

		 echo '<h1 class="text-center mrt50 master-title"><strong>'.$Title_page.'</strong></h1>';

	    echo '<div class="col-md-12 col-sm-12 col-xs-12 pad5 "> 
	    <div style="display: block;
    padding: 10px;
    border-radius: 10px;
    background: #feffff;
    max-width: 800px;
    margin: auto;
    color: #3c3c3c;
    margin-bottom: 20px;
    font-weight: bold;
    border: 1px solid #dcdcdc;">
	    <div id="showmoreindescr" style="height: 150px; overflow: hidden;">'.$descr.'</div><br>
	    <a onclick="ShowMoreInDescr()" id="btnshowmoreindescr">مشاهدة المزيد ...</a>
	    </div></div>';
	    
	}
	if ($hidecontent != ""){
	    echo '<div class="col-md-12 col-sm-12 col-xs-12 pad5 text-center"> 
	    <div style="display: none;"><br><br><br>
	    '.$hidecontent.'<br><br><br>
	    </div></div>';
	    
	}
	

	echo '<h2 class="hideclass">اسعار '.$PageTit0.' 2025</h2>';
    
    $checkiftag = getAllFrom('*' , 'tags' , 'WHERE status = 1 AND name = "'.$PageTit0.'" ', '');
    if(count($checkiftag) > 0 && $checkiftag[0]['hidecontent'] != ""){
	 echo '<h1 class="text-center mrt50 master-title"><strong>'.$Title_page.'</strong></h1>';

       echo '
       <div class="col-md-12 col-sm-12 col-xs-12 pad5 "> 
	    <div style="display: block;
    padding: 10px;
    border-radius: 10px;
    background: #feffff;
    max-width: 800px;
    margin: auto;
    color: #3c3c3c;
    margin-bottom: 20px;
    font-weight: bold;
    border: 1px solid #dcdcdc;">
	    <div id="showmoreindescr" style="height: 150px; overflow: hidden;">'.$checkiftag[0]['hidecontent'].'</div><br>
	    <a onclick="ShowMoreInDescr()" id="btnshowmoreindescr">مشاهدة المزيد ...</a>
	    </div></div>
       ';
    }
	
	$posts = getAllFrom('*' , 'posts' , 'WHERE status = 1 AND show_this = 1 AND (title LIKE "%'.$NavTypeahead.'%"  OR descr LIKE "%'.$NavTypeahead.'%" OR tags LIKE "%'.$NavTypeahead.'%") ', 'ORDER BY id DESC LIMIT '.GetTableSet ('LimitInIndexPosts'));
	if (count($posts) > 0 ){
		//echo '<div class="container-fluid">';
		echo '<div class="container">';


		echo '<h2 class="text-center mrt50 master-title">آخر عروض تكييف '.$PageTit0.'</h2>';
		
		for ($i=0; $i <= count($posts)-1 ; $i++) { 
				GetPost($posts[$i]['id']);
		}
		echo '<div class="col-md-12 col-sm-12 col-xs-12 pad5 text-center"> 	
					<h2 class="hideclass">اسعار تكييفات</h2>
					<a href="Posts.php" class="master-btn">شاهد كل العروض</a>	  
				</div>
			';
		echo '</div>';
	}


	$products = getAllFrom('*' , 'products' , 'WHERE status = 1 AND (title LIKE "%'.$NavTypeahead.'%" OR descr LIKE "%'.$NavTypeahead.'%" OR tags LIKE "%'.$NavTypeahead.'%") ', 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC ');
	if (count($products) > 0){
		$totalpage = ceil(count($products) / $ProductsPerPageInProducts);
		echo '<div class="container">';
		echo '<h2 class="text-center mrt50 master-title">تكييف '.$PageTit0.'</h2>';
		echo '<ul id="results">';
		if (count($products) > 0){
		    for ($i=0; $i <= count($products)-1 ; $i++) { 
        		GetProduct($products[$i]['id']);
            }
		}
		
		echo '</ul>';
		echo '</div>';
		
		
		$product_list =getAllFrom('*' , 'products' , 'WHERE status = 1 AND (title LIKE "%'.$NavTypeahead.'%" OR descr LIKE "%'.$NavTypeahead.'%" OR tags LIKE "%'.$NavTypeahead.'%") ', 'ORDER BY case price+ 0 WHEN 0 THEN 1 ELSE 0 END ASC, price+ 0 ASC ');
		
		if (count($product_list) > 0){
		echo '<div class="container text-center">';
		echo '<div class="space"></div><div class="space"></div><div class="space"></div>';
		echo '<div class="price-list">';
		
	    echo '<h2 class="price-list-title"> اسعار تكييف '.$NavTypeahead.' 2025 <span>آخر تحديث '.date("Y/m/d" , strtotime(date("Y/m/d") . ' -2 days')).'</span></h2>';
		
		echo '<table class="table table-bordered table-hover">';
		echo '<thead><tr><th>أفضل اسعار تكييفات '.$NavTypeahead.'</th><th>مواصفات تكييفات '.$NavTypeahead.'</th><th>سعر تكييفات '.$NavTypeahead.'</th></tr></thead><tbody>';
		for ($i=0; $i <= count($product_list)-1 ; $i++) { 
			$lpw = getAllFrom('*' , 'power' , 'WHERE status = 1 AND id = "'.$product_list[$i]['powerid'].'"' , '');
			if (count($lpw) >0){
				$po = $lpw[0]['name'];
			}else{
				$po = 'حصان';
			}
			echo '<tr>
					<td>
						<h4><a href="AirConditioning.php?Brand='.$MasterBrand[0]['en_name'].'&Model='.$product_list[$i]['title'].'&ID='.$product_list[$i]['id'].'">'.'سعر '.$product_list[$i]['title'].'</a></h4>
					</td>
					<td>
						<h4><a href="AirConditioning.php?Brand='.$MasterBrand[0]['en_name'].'&Model='.$product_list[$i]['title'].'&ID='.$product_list[$i]['id'].'">'.$po.' '.str_replace('/' , '' , $product_list[$i]['type']).'</a></h4>
					</td>
					<td>
						<h4><a href="AirConditioning.php?Brand='.$MasterBrand[0]['en_name'].'&Model='.$product_list[$i]['title'].'&ID='.$product_list[$i]['id'].'" style="display: none; ><i class="fa fa-tag" aria-hidden="true"></i><b>'.$product_list[$i]['price'].'</b> EGP</a></h4>
					</td>
				</tr>';
		}
		echo '</tbody></table>';
		echo '</div>';
		echo '</div>';
	}
	}else{
	    echo '<div class="container">';
		echo '<h2 class="text-center mrt50 master-title">تكييف '.$PageTit0.'</h2>';
		
		echo '<div class="col-md-12 col-xs-12 text-center" ">لا يوجد فى الوقت الحالي</div>';
		echo '</div>';
	}
	

}else{
	//get index page;
	header('Location: index.php'); exit();
}
//--------------------------------------------------------------------------------------






include('footer.php'); 
ob_end_flush();
?>
<?php
if ( (isset($_GET['typeahead']) && trim($_GET['typeahead']) != "") || (isset($_GET['AirConditioningBrand']) && trim($_GET['AirConditioningBrand']) != "") ){
?>
<script>
var PageNumper = 0; 
var total_pages = parseInt("<?php echo $totalpage ;?>");
$(document).ready(function() {
//	GetProductsAjax();
});

function GetProductsAjax(){
	$('#loadmore').html('<i class="fa fa-spinner fa-spin" style="" aria-hidden="true"></i>');

	$.post("ajax.php", {'page':PageNumper ,shpbrand:"<?php echo $shpbrand ; ?>" , shpower:"<?php echo $shpower ; ?>" , 'type':'products','PerPage':"<?php echo $ProductsPerPageInProducts ; ?>"} , function(data){           
		PageNumper++;
		$('#results').append(data);
		if(PageNumper < total_pages) {
			$('#loadmore').html('<a onclick="GetProductsAjax()" class="master-btn">عرض المزيد</a>');
		}else{
			$('#loadmore').html("");
		}
	});
}
</script>
<?php
}
?>